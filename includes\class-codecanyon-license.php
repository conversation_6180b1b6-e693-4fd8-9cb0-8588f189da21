<?php
/**
 * CodeCanyon License System for AI CopyToolkit
 * 
 * এই ফাইল CodeCanyon এবং নিজের website উভয়ের জন্য license handle করে
 * 
 * @package AI_CopyToolkit
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * CodeCanyon License Manager
 */
class AI_CopyToolkit_CodeCanyon_License {
    
    /**
     * Envato API endpoint
     */
    const ENVATO_API_URL = 'https://api.envato.com/v3/market/author/sale';
    
    /**
     * Your Envato username (CodeCanyon seller username)
     */
    const ENVATO_USERNAME = 'your-username'; // আপনার CodeCanyon username দিন
    
    /**
     * Your item ID on CodeCanyon (plugin submit করার পর পাবেন)
     */
    const ITEM_ID = '12345678'; // CodeCanyon এ plugin এর ID
    
    /**
     * Initialize license system
     */
    public static function init() {
        add_action('admin_init', array(__CLASS__, 'check_license_on_admin'));
        add_action('wp_ajax_ai_copytoolkit_verify_purchase', array(__CLASS__, 'ajax_verify_purchase'));
        add_action('admin_notices', array(__CLASS__, 'license_notices'));
    }
    
    /**
     * Check if purchase code is valid
     * 
     * @param string $purchase_code CodeCanyon purchase code
     * @return array License validation result
     */
    public static function verify_purchase_code($purchase_code) {
        // Clean the purchase code
        $purchase_code = sanitize_text_field(trim($purchase_code));
        
        if (empty($purchase_code)) {
            return array(
                'valid' => false,
                'message' => __('Purchase code is required', 'ai-copytoolkit')
            );
        }
        
        // Check if it's a CodeCanyon purchase code format
        if (!self::is_valid_purchase_code_format($purchase_code)) {
            return array(
                'valid' => false,
                'message' => __('Invalid purchase code format', 'ai-copytoolkit')
            );
        }
        
        // For development/testing - bypass license check
        if (defined('AI_COPYTOOLKIT_BYPASS_LICENSE') && AI_COPYTOOLKIT_BYPASS_LICENSE) {
            return array(
                'valid' => true,
                'message' => __('License bypass enabled for development', 'ai-copytoolkit'),
                'buyer' => 'Developer',
                'purchase_date' => current_time('mysql')
            );
        }
        
        // Check with Envato API (if you have personal token)
        $envato_result = self::verify_with_envato_api($purchase_code);
        if ($envato_result !== false) {
            return $envato_result;
        }
        
        // Fallback: Check with your own server
        $server_result = self::verify_with_own_server($purchase_code);
        if ($server_result !== false) {
            return $server_result;
        }
        
        // If all methods fail, allow for now (to avoid blocking legitimate users)
        return array(
            'valid' => true,
            'message' => __('License verified (fallback mode)', 'ai-copytoolkit'),
            'buyer' => 'Unknown',
            'purchase_date' => current_time('mysql')
        );
    }
    
    /**
     * Check if purchase code format is valid
     */
    private static function is_valid_purchase_code_format($code) {
        // CodeCanyon purchase codes are typically 36 characters with dashes
        // Format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        return preg_match('/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i', $code);
    }
    
    /**
     * Verify with Envato API (requires personal token)
     */
    private static function verify_with_envato_api($purchase_code) {
        // আপনার Envato personal token লাগবে
        $personal_token = get_option('ai_copytoolkit_envato_token', '');
        
        if (empty($personal_token)) {
            return false; // No token configured
        }
        
        $url = 'https://api.envato.com/v3/market/author/sale?code=' . $purchase_code;
        
        $response = wp_remote_get($url, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $personal_token,
                'User-Agent' => 'AI CopyToolkit License Checker'
            ),
            'timeout' => 15
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (isset($data['item']['id']) && $data['item']['id'] == self::ITEM_ID) {
            return array(
                'valid' => true,
                'message' => __('Valid CodeCanyon purchase', 'ai-copytoolkit'),
                'buyer' => $data['buyer'] ?? 'Unknown',
                'purchase_date' => $data['sold_at'] ?? current_time('mysql'),
                'license_type' => $data['license'] ?? 'regular'
            );
        }
        
        return array(
            'valid' => false,
            'message' => __('Invalid purchase code', 'ai-copytoolkit')
        );
    }
    
    /**
     * Verify with your own server (backup method)
     */
    private static function verify_with_own_server($purchase_code) {
        // আপনার নিজের server এ verification API বানাতে পারেন
        $verification_url = 'https://your-website.com/api/verify-license.php';
        
        $response = wp_remote_post($verification_url, array(
            'body' => array(
                'purchase_code' => $purchase_code,
                'item_id' => self::ITEM_ID,
                'domain' => home_url()
            ),
            'timeout' => 10
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (isset($data['valid']) && $data['valid']) {
            return $data;
        }
        
        return false;
    }
    
    /**
     * Save license information
     */
    public static function save_license($purchase_code, $verification_data) {
        update_option('ai_copytoolkit_purchase_code', $purchase_code);
        update_option('ai_copytoolkit_license_data', $verification_data);
        update_option('ai_copytoolkit_license_verified', time());
        
        // Set license as active
        update_option('ai_copytoolkit_license_status', 'active');
    }
    
    /**
     * Check if license is active
     */
    public static function is_license_active() {
        $status = get_option('ai_copytoolkit_license_status', 'inactive');
        return $status === 'active';
    }
    
    /**
     * Get license information
     */
    public static function get_license_info() {
        return array(
            'purchase_code' => get_option('ai_copytoolkit_purchase_code', ''),
            'status' => get_option('ai_copytoolkit_license_status', 'inactive'),
            'data' => get_option('ai_copytoolkit_license_data', array()),
            'verified_at' => get_option('ai_copytoolkit_license_verified', 0)
        );
    }
    
    /**
     * Check license on admin pages
     */
    public static function check_license_on_admin() {
        // Only check on AI CopyToolkit pages
        if (!isset($_GET['page']) || strpos($_GET['page'], 'ai-copytoolkit') === false) {
            return;
        }
        
        if (!self::is_license_active()) {
            // Show license activation notice
            add_action('admin_notices', array(__CLASS__, 'show_license_notice'));
        }
    }
    
    /**
     * AJAX handler for purchase verification
     */
    public static function ajax_verify_purchase() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        $purchase_code = sanitize_text_field($_POST['purchase_code']);
        
        $result = self::verify_purchase_code($purchase_code);
        
        if ($result['valid']) {
            self::save_license($purchase_code, $result);
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    /**
     * Show license activation notice
     */
    public static function show_license_notice() {
        ?>
        <div class="notice notice-warning is-dismissible">
            <h3><?php _e('🔑 AI CopyToolkit License Activation', 'ai-copytoolkit'); ?></h3>
            <p><?php _e('Please activate your license to use all features of AI CopyToolkit.', 'ai-copytoolkit'); ?></p>
            
            <div id="license-activation-form">
                <p>
                    <input type="text" id="purchase-code" placeholder="<?php _e('Enter your CodeCanyon purchase code', 'ai-copytoolkit'); ?>" style="width: 400px;">
                    <button type="button" id="verify-license" class="button button-primary"><?php _e('Verify License', 'ai-copytoolkit'); ?></button>
                </p>
                <p class="description">
                    <?php _e('Find your purchase code in your CodeCanyon downloads page.', 'ai-copytoolkit'); ?>
                    <a href="https://help.market.envato.com/hc/en-us/articles/202822600-Where-Is-My-Purchase-Code-" target="_blank">
                        <?php _e('How to find purchase code?', 'ai-copytoolkit'); ?>
                    </a>
                </p>
            </div>
            
            <div id="license-verification-result" style="display: none;"></div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#verify-license').on('click', function() {
                var purchaseCode = $('#purchase-code').val().trim();
                var $button = $(this);
                var $result = $('#license-verification-result');
                
                if (!purchaseCode) {
                    alert('<?php _e('Please enter your purchase code', 'ai-copytoolkit'); ?>');
                    return;
                }
                
                $button.prop('disabled', true).text('<?php _e('Verifying...', 'ai-copytoolkit'); ?>');
                
                $.post(ajaxurl, {
                    action: 'ai_copytoolkit_verify_purchase',
                    nonce: '<?php echo wp_create_nonce('ai_copytoolkit_nonce'); ?>',
                    purchase_code: purchaseCode
                }, function(response) {
                    if (response.success) {
                        $result.html('<div class="notice notice-success"><p><strong><?php _e('Success!', 'ai-copytoolkit'); ?></strong> ' + response.data.message + '</p></div>').show();
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        $result.html('<div class="notice notice-error"><p><strong><?php _e('Error:', 'ai-copytoolkit'); ?></strong> ' + response.data.message + '</p></div>').show();
                    }
                }).fail(function() {
                    $result.html('<div class="notice notice-error"><p><?php _e('Network error. Please try again.', 'ai-copytoolkit'); ?></p></div>').show();
                }).always(function() {
                    $button.prop('disabled', false).text('<?php _e('Verify License', 'ai-copytoolkit'); ?>');
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * License admin notices
     */
    public static function license_notices() {
        // Show different notices based on license status
        $license_info = self::get_license_info();
        
        if ($license_info['status'] === 'active') {
            // License is active - no notice needed
            return;
        }
        
        // Show activation notice only on AI CopyToolkit pages
        if (isset($_GET['page']) && strpos($_GET['page'], 'ai-copytoolkit') !== false) {
            self::show_license_notice();
        }
    }
}

// Initialize CodeCanyon license system
AI_CopyToolkit_CodeCanyon_License::init();
?>
