<?php
/**
 * Dashboard page template
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get plugin settings
$settings = AI_CopyToolkit::get_settings();
$api_configured = !empty($settings['api_key']);

// Get some stats
global $wpdb;
$user_id = get_current_user_id();
$history_table = AI_CopyToolkit_Database::get_table_name('history');
$templates_table = AI_CopyToolkit_Database::get_table_name('templates');
$usage_table = AI_CopyToolkit_Database::get_table_name('api_usage');

$total_generations = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM $history_table WHERE user_id = %d AND status = 'completed'",
    $user_id
));

$total_tokens = $wpdb->get_var($wpdb->prepare(
    "SELECT SUM(tokens_used) FROM $history_table WHERE user_id = %d AND status = 'completed'",
    $user_id
));

$recent_generations = $wpdb->get_results($wpdb->prepare("
    SELECT h.*, t.name as template_name 
    FROM $history_table h
    LEFT JOIN $templates_table t ON h.template_id = t.id
    WHERE h.user_id = %d AND h.status = 'completed'
    ORDER BY h.created_at DESC
    LIMIT 5
", $user_id));

$available_templates = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_active = 1");
?>

<div class="wrap ai-copytoolkit-dashboard">
    <h1 class="wp-heading-inline">
        <?php _e('AI CopyToolkit Dashboard', 'ai-copytoolkit'); ?>
    </h1>
    
    <?php if (!$api_configured): ?>
        <div class="notice notice-warning">
            <p>
                <strong><?php _e('Welcome to AI CopyToolkit!', 'ai-copytoolkit'); ?></strong>
                <?php printf(
                    __('To get started, please <a href="%s">configure your OpenRouter API key</a> in the settings.', 'ai-copytoolkit'),
                    admin_url('admin.php?page=ai-copytoolkit-settings')
                ); ?>
            </p>
        </div>
    <?php endif; ?>
    
    <div class="ai-copytoolkit-dashboard-grid">
        <!-- Quick Stats -->
        <div class="ai-copytoolkit-card ai-copytoolkit-stats">
            <h2><?php _e('Quick Stats', 'ai-copytoolkit'); ?></h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format($total_generations ?: 0); ?></div>
                    <div class="stat-label"><?php _e('Content Generated', 'ai-copytoolkit'); ?></div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format($total_tokens ?: 0); ?></div>
                    <div class="stat-label"><?php _e('Tokens Used', 'ai-copytoolkit'); ?></div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format($available_templates); ?></div>
                    <div class="stat-label"><?php _e('Templates Available', 'ai-copytoolkit'); ?></div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="ai-copytoolkit-card ai-copytoolkit-quick-actions">
            <h2><?php _e('Quick Actions', 'ai-copytoolkit'); ?></h2>
            <div class="action-buttons">
                <a href="<?php echo admin_url('admin.php?page=ai-copytoolkit-generate'); ?>" class="button button-primary button-large">
                    <span class="dashicons dashicons-edit-large"></span>
                    <?php _e('Generate Content', 'ai-copytoolkit'); ?>
                </a>
                <a href="<?php echo admin_url('admin.php?page=ai-copytoolkit-templates'); ?>" class="button button-secondary">
                    <span class="dashicons dashicons-admin-page"></span>
                    <?php _e('Browse Templates', 'ai-copytoolkit'); ?>
                </a>
                <a href="<?php echo admin_url('admin.php?page=ai-copytoolkit-history'); ?>" class="button button-secondary">
                    <span class="dashicons dashicons-backup"></span>
                    <?php _e('View History', 'ai-copytoolkit'); ?>
                </a>
            </div>
        </div>
        
        <!-- Recent Generations -->
        <div class="ai-copytoolkit-card ai-copytoolkit-recent">
            <h2><?php _e('Recent Generations', 'ai-copytoolkit'); ?></h2>
            <?php if (!empty($recent_generations)): ?>
                <div class="recent-list">
                    <?php foreach ($recent_generations as $generation): ?>
                        <div class="recent-item">
                            <div class="recent-header">
                                <strong><?php echo esc_html($generation->template_name ?: __('Custom Prompt', 'ai-copytoolkit')); ?></strong>
                                <span class="recent-date"><?php echo human_time_diff(strtotime($generation->created_at), current_time('timestamp')) . ' ' . __('ago', 'ai-copytoolkit'); ?></span>
                            </div>
                            <div class="recent-content">
                                <?php echo esc_html(wp_trim_words($generation->generated_content, 20)); ?>
                            </div>
                            <div class="recent-meta">
                                <span class="model-used"><?php echo esc_html($generation->model_used); ?></span>
                                <span class="tokens-used"><?php echo number_format($generation->tokens_used) . ' ' . __('tokens', 'ai-copytoolkit'); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="recent-footer">
                    <a href="<?php echo admin_url('admin.php?page=ai-copytoolkit-history'); ?>" class="button">
                        <?php _e('View All History', 'ai-copytoolkit'); ?>
                    </a>
                </div>
            <?php else: ?>
                <div class="no-content">
                    <p><?php _e('No content generated yet.', 'ai-copytoolkit'); ?></p>
                    <a href="<?php echo admin_url('admin.php?page=ai-copytoolkit-generate'); ?>" class="button button-primary">
                        <?php _e('Generate Your First Content', 'ai-copytoolkit'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Getting Started -->
        <div class="ai-copytoolkit-card ai-copytoolkit-getting-started">
            <h2><?php _e('Getting Started', 'ai-copytoolkit'); ?></h2>
            <div class="getting-started-steps">
                <div class="step <?php echo $api_configured ? 'completed' : 'current'; ?>">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3><?php _e('Configure API Key', 'ai-copytoolkit'); ?></h3>
                        <p><?php _e('Set up your OpenRouter API key to access AI models.', 'ai-copytoolkit'); ?></p>
                        <?php if (!$api_configured): ?>
                            <a href="<?php echo admin_url('admin.php?page=ai-copytoolkit-settings'); ?>" class="button button-small">
                                <?php _e('Configure Now', 'ai-copytoolkit'); ?>
                            </a>
                        <?php else: ?>
                            <span class="dashicons dashicons-yes-alt"></span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="step <?php echo ($api_configured && $total_generations == 0) ? 'current' : ($total_generations > 0 ? 'completed' : ''); ?>">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3><?php _e('Generate Content', 'ai-copytoolkit'); ?></h3>
                        <p><?php _e('Choose a template and generate your first piece of content.', 'ai-copytoolkit'); ?></p>
                        <?php if ($api_configured && $total_generations == 0): ?>
                            <a href="<?php echo admin_url('admin.php?page=ai-copytoolkit-generate'); ?>" class="button button-small">
                                <?php _e('Start Generating', 'ai-copytoolkit'); ?>
                            </a>
                        <?php elseif ($total_generations > 0): ?>
                            <span class="dashicons dashicons-yes-alt"></span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="step <?php echo $total_generations > 5 ? 'completed' : ''; ?>">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3><?php _e('Explore Features', 'ai-copytoolkit'); ?></h3>
                        <p><?php _e('Try different templates, create custom ones, and explore the history.', 'ai-copytoolkit'); ?></p>
                        <?php if ($total_generations > 5): ?>
                            <span class="dashicons dashicons-yes-alt"></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tips & Tricks -->
        <div class="ai-copytoolkit-card ai-copytoolkit-tips">
            <h2><?php _e('Tips & Tricks', 'ai-copytoolkit'); ?></h2>
            <ul class="tips-list">
                <li>
                    <strong><?php _e('Be Specific:', 'ai-copytoolkit'); ?></strong>
                    <?php _e('The more specific your input, the better the AI-generated content will be.', 'ai-copytoolkit'); ?>
                </li>
                <li>
                    <strong><?php _e('Try Different Models:', 'ai-copytoolkit'); ?></strong>
                    <?php _e('Different AI models excel at different types of content. Experiment to find the best fit.', 'ai-copytoolkit'); ?>
                </li>
                <li>
                    <strong><?php _e('Adjust Temperature:', 'ai-copytoolkit'); ?></strong>
                    <?php _e('Lower values (0.3-0.5) for factual content, higher values (0.7-0.9) for creative content.', 'ai-copytoolkit'); ?>
                </li>
                <li>
                    <strong><?php _e('Save Custom Templates:', 'ai-copytoolkit'); ?></strong>
                    <?php _e('Create and save templates for content types you generate frequently.', 'ai-copytoolkit'); ?>
                </li>
            </ul>
        </div>
        
        <!-- System Status -->
        <div class="ai-copytoolkit-card ai-copytoolkit-status">
            <h2><?php _e('System Status', 'ai-copytoolkit'); ?></h2>
            <div class="status-items">
                <div class="status-item">
                    <span class="status-label"><?php _e('API Connection:', 'ai-copytoolkit'); ?></span>
                    <span class="status-value <?php echo $api_configured ? 'status-good' : 'status-warning'; ?>">
                        <?php echo $api_configured ? __('Connected', 'ai-copytoolkit') : __('Not Configured', 'ai-copytoolkit'); ?>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label"><?php _e('Plugin Version:', 'ai-copytoolkit'); ?></span>
                    <span class="status-value"><?php echo AI_COPYTOOLKIT_VERSION; ?></span>
                </div>
                <div class="status-item">
                    <span class="status-label"><?php _e('WordPress Version:', 'ai-copytoolkit'); ?></span>
                    <span class="status-value"><?php echo get_bloginfo('version'); ?></span>
                </div>
                <div class="status-item">
                    <span class="status-label"><?php _e('PHP Version:', 'ai-copytoolkit'); ?></span>
                    <span class="status-value"><?php echo PHP_VERSION; ?></span>
                </div>
            </div>
        </div>
    </div>
</div>
