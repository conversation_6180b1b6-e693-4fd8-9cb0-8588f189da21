/**
 * AI CopyToolkit Admin JavaScript
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    // Global variables
    window.AICopyToolkit = window.AICopyToolkit || {};
    
    const AICopyToolkit = {
        
        /**
         * Initialize the admin interface
         */
        init: function() {
            this.bindEvents();
            this.initComponents();
        },
        
        /**
         * Bind global events
         */
        bindEvents: function() {
            // Global copy functionality
            $(document).on('click', '[data-copy]', this.copyToClipboard);
            
            // Global modal functionality
            $(document).on('click', '.modal-close, .modal-overlay', this.closeModal);
            $(document).on('keydown', this.handleKeydown);
            
            // Global form validation
            $(document).on('submit', 'form[data-validate]', this.validateForm);
            
            // Global AJAX error handling
            $(document).ajaxError(this.handleAjaxError);
            
            // Dismissible notices
            $(document).on('click', '.notice-dismiss', this.dismissNotice);
        },
        
        /**
         * Initialize components
         */
        initComponents: function() {
            this.initTooltips();
            this.initTabs();
            this.initAccordions();
            this.initCounters();
        },
        
        /**
         * Copy text to clipboard
         */
        copyToClipboard: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const text = $button.data('copy') || $button.attr('data-copy');
            const target = $button.data('target');
            
            let textToCopy = text;
            
            if (target) {
                const $target = $(target);
                textToCopy = $target.is('input, textarea') ? $target.val() : $target.text();
            }
            
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(textToCopy).then(function() {
                    AICopyToolkit.showNotice(aiCopyToolkit.strings.copied, 'success');
                }).catch(function() {
                    AICopyToolkit.fallbackCopy(textToCopy);
                });
            } else {
                AICopyToolkit.fallbackCopy(textToCopy);
            }
        },
        
        /**
         * Fallback copy method for older browsers
         */
        fallbackCopy: function(text) {
            const $temp = $('<textarea>').val(text).appendTo('body').select();
            
            try {
                document.execCommand('copy');
                this.showNotice(aiCopyToolkit.strings.copied, 'success');
            } catch (err) {
                this.showNotice(aiCopyToolkit.strings.copy_failed, 'error');
            }
            
            $temp.remove();
        },
        
        /**
         * Show notification
         */
        showNotice: function(message, type, duration) {
            type = type || 'info';
            duration = duration || 3000;
            
            const $notice = $('<div class="notice notice-' + type + ' is-dismissible">')
                .html('<p>' + message + '</p>')
                .hide();
            
            // Find the best place to insert the notice
            let $target = $('.wrap h1').first();
            if ($target.length === 0) {
                $target = $('.wrap').first();
            }
            
            $notice.insertAfter($target).slideDown();
            
            // Auto-dismiss after duration
            if (duration > 0) {
                setTimeout(function() {
                    $notice.slideUp(function() {
                        $(this).remove();
                    });
                }, duration);
            }
            
            return $notice;
        },
        
        /**
         * Close modal
         */
        closeModal: function(e) {
            if (e.target === this || $(e.target).hasClass('modal-close')) {
                $(this).closest('.ai-copytoolkit-modal').hide();
            }
        },
        
        /**
         * Handle keydown events
         */
        handleKeydown: function(e) {
            // Close modal on Escape key
            if (e.keyCode === 27) {
                $('.ai-copytoolkit-modal:visible').hide();
            }
        },
        
        /**
         * Validate form
         */
        validateForm: function(e) {
            const $form = $(this);
            let isValid = true;
            
            // Clear previous errors
            $form.find('.field-error').removeClass('field-error');
            $form.find('.error-message').remove();
            
            // Validate required fields
            $form.find('[required]').each(function() {
                const $field = $(this);
                const value = $field.val().trim();
                
                if (!value) {
                    isValid = false;
                    $field.addClass('field-error');
                    $field.after('<div class="error-message">This field is required.</div>');
                }
            });
            
            // Validate email fields
            $form.find('input[type="email"]').each(function() {
                const $field = $(this);
                const value = $field.val().trim();
                
                if (value && !AICopyToolkit.isValidEmail(value)) {
                    isValid = false;
                    $field.addClass('field-error');
                    $field.after('<div class="error-message">Please enter a valid email address.</div>');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                AICopyToolkit.showNotice('Please correct the errors below.', 'error');
            }
            
            return isValid;
        },
        
        /**
         * Handle AJAX errors
         */
        handleAjaxError: function(event, jqXHR, ajaxSettings, thrownError) {
            if (jqXHR.status === 0) {
                return; // Ignore aborted requests
            }
            
            let message = 'An error occurred. Please try again.';
            
            if (jqXHR.responseJSON && jqXHR.responseJSON.data) {
                message = jqXHR.responseJSON.data;
            } else if (jqXHR.responseText) {
                try {
                    const response = JSON.parse(jqXHR.responseText);
                    if (response.data) {
                        message = response.data;
                    }
                } catch (e) {
                    // Use default message
                }
            }
            
            AICopyToolkit.showNotice(message, 'error');
        },
        
        /**
         * Dismiss notice
         */
        dismissNotice: function() {
            $(this).closest('.notice').slideUp(function() {
                $(this).remove();
            });
        },
        
        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            $('[data-tooltip]').each(function() {
                const $element = $(this);
                const tooltip = $element.data('tooltip');
                
                $element.attr('title', tooltip);
            });
        },
        
        /**
         * Initialize tabs
         */
        initTabs: function() {
            $('.ai-copytoolkit-tabs').each(function() {
                const $container = $(this);
                const $tabs = $container.find('.tab-button');
                const $contents = $container.find('.tab-content');
                
                $tabs.on('click', function(e) {
                    e.preventDefault();
                    
                    const target = $(this).data('tab');
                    
                    $tabs.removeClass('active');
                    $(this).addClass('active');
                    
                    $contents.hide();
                    $('#' + target).show();
                });
            });
        },
        
        /**
         * Initialize accordions
         */
        initAccordions: function() {
            $('.ai-copytoolkit-accordion').each(function() {
                const $accordion = $(this);
                const $headers = $accordion.find('.accordion-header');
                
                $headers.on('click', function() {
                    const $header = $(this);
                    const $content = $header.next('.accordion-content');
                    const isOpen = $header.hasClass('open');
                    
                    // Close all other accordions
                    $accordion.find('.accordion-header').removeClass('open');
                    $accordion.find('.accordion-content').slideUp();
                    
                    if (!isOpen) {
                        $header.addClass('open');
                        $content.slideDown();
                    }
                });
            });
        },
        
        /**
         * Initialize counters
         */
        initCounters: function() {
            $('.counter').each(function() {
                const $counter = $(this);
                const target = parseInt($counter.data('target'), 10);
                const duration = parseInt($counter.data('duration'), 10) || 2000;
                
                $({ count: 0 }).animate({ count: target }, {
                    duration: duration,
                    easing: 'swing',
                    step: function() {
                        $counter.text(Math.floor(this.count));
                    },
                    complete: function() {
                        $counter.text(target);
                    }
                });
            });
        },
        
        /**
         * Validate email address
         */
        isValidEmail: function(email) {
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },
        
        /**
         * Format number with commas
         */
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },
        
        /**
         * Truncate text
         */
        truncateText: function(text, length) {
            if (text.length <= length) {
                return text;
            }
            return text.substring(0, length) + '...';
        },
        
        /**
         * Debounce function
         */
        debounce: function(func, wait, immediate) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                const later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        },
        
        /**
         * Throttle function
         */
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },
        
        /**
         * Make AJAX request with proper error handling
         */
        ajaxRequest: function(action, data, options) {
            options = options || {};
            
            const defaultOptions = {
                type: 'POST',
                url: aiCopyToolkit.ajaxUrl,
                dataType: 'json',
                data: $.extend({
                    action: action,
                    nonce: aiCopyToolkit.nonce
                }, data)
            };
            
            return $.ajax($.extend(defaultOptions, options));
        },
        
        /**
         * Show loading state
         */
        showLoading: function($element, text) {
            text = text || 'Loading...';
            
            const $spinner = $('<span class="spinner is-active"></span>');
            const $text = $('<span class="loading-text">' + text + '</span>');
            
            $element.data('original-html', $element.html());
            $element.html('').append($spinner).append($text);
            $element.prop('disabled', true);
        },
        
        /**
         * Hide loading state
         */
        hideLoading: function($element) {
            const originalHtml = $element.data('original-html');
            if (originalHtml) {
                $element.html(originalHtml);
                $element.removeData('original-html');
            }
            $element.prop('disabled', false);
        }
    };
    
    // Content Generator specific functionality
    const ContentGenerator = {
        
        init: function() {
            this.bindEvents();
            this.loadModels();
        },
        
        bindEvents: function() {
            $(document).on('click', '.template-card', this.selectTemplate);
            $(document).on('click', '#generate-content', this.generateContent);
            $(document).on('input', '#custom-prompt', this.updateGenerateButton);
            $(document).on('change', '#model-select, #max-tokens, #temperature', this.updateSettings);
        },
        
        selectTemplate: function() {
            $('.template-card').removeClass('selected');
            $(this).addClass('selected');
            
            const templateId = $(this).data('template-id');
            ContentGenerator.loadTemplateParameters(templateId);
            ContentGenerator.updateGenerateButton();
        },
        
        loadTemplateParameters: function(templateId) {
            // Implementation would load template parameters
            $('#template-parameters').show();
        },
        
        generateContent: function() {
            const $button = $(this);
            AICopyToolkit.showLoading($button, aiCopyToolkit.strings.generating);
            
            const data = {
                template_id: $('.template-card.selected').data('template-id') || 0,
                custom_prompt: $('#custom-prompt').val(),
                parameters: {
                    model: $('#model-select').val(),
                    max_tokens: parseInt($('#max-tokens').val()),
                    temperature: parseFloat($('#temperature').val())
                }
            };
            
            AICopyToolkit.ajaxRequest('ai_copytoolkit_generate_content', data)
                .done(function(response) {
                    if (response.success) {
                        ContentGenerator.displayGeneratedContent(response.data);
                        AICopyToolkit.showNotice(aiCopyToolkit.strings.success, 'success');
                    } else {
                        AICopyToolkit.showNotice(response.data || aiCopyToolkit.strings.error, 'error');
                    }
                })
                .always(function() {
                    AICopyToolkit.hideLoading($button);
                });
        },
        
        displayGeneratedContent: function(data) {
            $('#content-output').val(data.content);
            $('#meta-model').text(data.model);
            $('#meta-tokens').text(AICopyToolkit.formatNumber(data.tokens_used));
            $('#meta-time').text(data.generation_time.toFixed(2) + 's');
            $('#generated-content').show();
        },
        
        updateGenerateButton: function() {
            const hasTemplate = $('.template-card.selected').length > 0;
            const hasCustomPrompt = $('#custom-prompt').val().trim() !== '';
            const canGenerate = hasTemplate || hasCustomPrompt;
            
            $('#generate-content').prop('disabled', !canGenerate);
        },
        
        updateSettings: function() {
            // Update UI based on settings changes
            if ($(this).attr('id') === 'temperature') {
                $('.temperature-value').text($(this).val());
            }
        },
        
        loadModels: function() {
            AICopyToolkit.ajaxRequest('ai_copytoolkit_get_models')
                .done(function(response) {
                    if (response.success) {
                        const $select = $('#model-select');
                        $select.empty();
                        
                        response.data.forEach(function(model) {
                            $select.append($('<option>', {
                                value: model.id,
                                text: model.name || model.id
                            }));
                        });
                    }
                });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        AICopyToolkit.init();
        
        // Initialize page-specific functionality
        if ($('.ai-copytoolkit-generate').length) {
            ContentGenerator.init();
        }
    });
    
    // Expose to global scope
    window.AICopyToolkit = AICopyToolkit;
    window.AICopyToolkitContentGenerator = ContentGenerator;
    
})(jQuery);
