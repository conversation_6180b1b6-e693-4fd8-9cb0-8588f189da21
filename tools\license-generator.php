<?php
/**
 * Simple License Key Generator for AI CopyToolkit
 * 
 * This is a simple tool to generate license keys for your plugin.
 * You can run this script to generate new license keys and add them to your license system.
 * 
 * Usage: php license-generator.php
 */

class AI_CopyToolkit_License_Generator {
    
    /**
     * Generate a new license key
     *
     * @param string $prefix
     * @param int $length
     * @return string
     */
    public static function generate_license_key($prefix = 'AICT', $length = 16) {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $key = $prefix . '-';
        
        for ($i = 0; $i < $length; $i++) {
            if ($i > 0 && $i % 4 === 0) {
                $key .= '-';
            }
            $key .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $key;
    }
    
    /**
     * Generate license data
     *
     * @param array $options
     * @return array
     */
    public static function generate_license_data($options = array()) {
        $defaults = array(
            'customer_name' => 'Customer Name',
            'customer_email' => '<EMAIL>',
            'expires' => 'lifetime', // or date like '2025-12-31'
            'activations_left' => 5,
            'status' => 'valid'
        );
        
        return array_merge($defaults, $options);
    }
    
    /**
     * Generate multiple licenses
     *
     * @param int $count
     * @param array $options
     * @return array
     */
    public static function generate_multiple_licenses($count = 10, $options = array()) {
        $licenses = array();
        
        for ($i = 0; $i < $count; $i++) {
            $key = self::generate_license_key();
            $data = self::generate_license_data($options);
            $licenses[$key] = $data;
        }
        
        return $licenses;
    }
    
    /**
     * Export licenses as PHP array
     *
     * @param array $licenses
     * @return string
     */
    public static function export_as_php_array($licenses) {
        $output = "<?php\n";
        $output .= "// Generated license keys for AI CopyToolkit\n";
        $output .= "// Add these to your includes/class-license.php file in the \$valid_licenses array\n\n";
        $output .= "return array(\n";
        
        foreach ($licenses as $key => $data) {
            $output .= "    '{$key}' => array(\n";
            foreach ($data as $field => $value) {
                if (is_string($value)) {
                    $output .= "        '{$field}' => '{$value}',\n";
                } else {
                    $output .= "        '{$field}' => {$value},\n";
                }
            }
            $output .= "    ),\n";
        }
        
        $output .= ");\n";
        
        return $output;
    }
    
    /**
     * Export licenses as JSON
     *
     * @param array $licenses
     * @return string
     */
    public static function export_as_json($licenses) {
        return json_encode($licenses, JSON_PRETTY_PRINT);
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    echo "AI CopyToolkit License Generator\n";
    echo "================================\n\n";
    
    // Get user input
    echo "How many licenses do you want to generate? (default: 5): ";
    $count = (int) trim(fgets(STDIN)) ?: 5;
    
    echo "Customer name (default: Customer Name): ";
    $customer_name = trim(fgets(STDIN)) ?: 'Customer Name';
    
    echo "Customer email (default: <EMAIL>): ";
    $customer_email = trim(fgets(STDIN)) ?: '<EMAIL>';
    
    echo "Expiration date (default: lifetime, or YYYY-MM-DD): ";
    $expires = trim(fgets(STDIN)) ?: 'lifetime';
    
    echo "Number of activations allowed (default: 5): ";
    $activations = (int) trim(fgets(STDIN)) ?: 5;
    
    // Generate licenses
    $options = array(
        'customer_name' => $customer_name,
        'customer_email' => $customer_email,
        'expires' => $expires,
        'activations_left' => $activations
    );
    
    $licenses = AI_CopyToolkit_License_Generator::generate_multiple_licenses($count, $options);
    
    echo "\nGenerated {$count} license keys:\n";
    echo "================================\n";
    
    foreach ($licenses as $key => $data) {
        echo "License Key: {$key}\n";
        echo "  Customer: {$data['customer_name']} ({$data['customer_email']})\n";
        echo "  Expires: {$data['expires']}\n";
        echo "  Activations: {$data['activations_left']}\n";
        echo "  Status: {$data['status']}\n\n";
    }
    
    // Export options
    echo "Export format:\n";
    echo "1. PHP Array (for adding to plugin)\n";
    echo "2. JSON (for external systems)\n";
    echo "3. Both\n";
    echo "Choose (1-3): ";
    
    $format = (int) trim(fgets(STDIN)) ?: 1;
    
    if ($format === 1 || $format === 3) {
        $php_output = AI_CopyToolkit_License_Generator::export_as_php_array($licenses);
        file_put_contents('generated-licenses.php', $php_output);
        echo "PHP array saved to: generated-licenses.php\n";
    }
    
    if ($format === 2 || $format === 3) {
        $json_output = AI_CopyToolkit_License_Generator::export_as_json($licenses);
        file_put_contents('generated-licenses.json', $json_output);
        echo "JSON data saved to: generated-licenses.json\n";
    }
    
    echo "\nTo use these licenses:\n";
    echo "1. Copy the generated license keys\n";
    echo "2. Add them to includes/class-license.php in the \$valid_licenses array\n";
    echo "3. Or set up a remote license server to validate them\n";
    
} else {
    // Web interface
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>AI CopyToolkit License Generator</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
            button { background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
            button:hover { background: #005a87; }
            .result { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px; }
            .license-key { font-family: monospace; background: #e9ecef; padding: 5px; border-radius: 3px; }
        </style>
    </head>
    <body>
        <h1>AI CopyToolkit License Generator</h1>
        
        <form method="post">
            <div class="form-group">
                <label>Number of licenses:</label>
                <input type="number" name="count" value="<?php echo $_POST['count'] ?? 5; ?>" min="1" max="100">
            </div>
            
            <div class="form-group">
                <label>Customer Name:</label>
                <input type="text" name="customer_name" value="<?php echo $_POST['customer_name'] ?? 'Customer Name'; ?>">
            </div>
            
            <div class="form-group">
                <label>Customer Email:</label>
                <input type="email" name="customer_email" value="<?php echo $_POST['customer_email'] ?? '<EMAIL>'; ?>">
            </div>
            
            <div class="form-group">
                <label>Expiration:</label>
                <select name="expires">
                    <option value="lifetime" <?php echo ($_POST['expires'] ?? '') === 'lifetime' ? 'selected' : ''; ?>>Lifetime</option>
                    <option value="2025-12-31" <?php echo ($_POST['expires'] ?? '') === '2025-12-31' ? 'selected' : ''; ?>>2025-12-31</option>
                    <option value="2026-12-31" <?php echo ($_POST['expires'] ?? '') === '2026-12-31' ? 'selected' : ''; ?>>2026-12-31</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>Activations Allowed:</label>
                <input type="number" name="activations" value="<?php echo $_POST['activations'] ?? 5; ?>" min="1">
            </div>
            
            <button type="submit">Generate Licenses</button>
        </form>
        
        <?php
        if ($_POST) {
            $options = array(
                'customer_name' => $_POST['customer_name'],
                'customer_email' => $_POST['customer_email'],
                'expires' => $_POST['expires'],
                'activations_left' => (int) $_POST['activations']
            );
            
            $licenses = AI_CopyToolkit_License_Generator::generate_multiple_licenses((int) $_POST['count'], $options);
            
            echo '<div class="result">';
            echo '<h2>Generated License Keys</h2>';
            
            foreach ($licenses as $key => $data) {
                echo '<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">';
                echo '<div class="license-key">' . $key . '</div>';
                echo '<small>Customer: ' . $data['customer_name'] . ' | Expires: ' . $data['expires'] . ' | Activations: ' . $data['activations_left'] . '</small>';
                echo '</div>';
            }
            
            echo '<h3>PHP Array (copy to includes/class-license.php)</h3>';
            echo '<textarea style="width: 100%; height: 200px; font-family: monospace;">';
            echo htmlspecialchars(AI_CopyToolkit_License_Generator::export_as_php_array($licenses));
            echo '</textarea>';
            
            echo '</div>';
        }
        ?>
    </body>
    </html>
    <?php
}
?>
