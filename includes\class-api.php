<?php
/**
 * OpenRouter API integration class
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI_CopyToolkit_API class
 */
class AI_CopyToolkit_API {
    
    /**
     * Instance
     *
     * @var AI_CopyToolkit_API
     */
    private static $instance = null;
    
    /**
     * OpenRouter API base URL
     *
     * @var string
     */
    private $api_base_url = 'https://openrouter.ai/api/v1';
    
    /**
     * API key
     *
     * @var string
     */
    private $api_key;
    
    /**
     * Rate limiting
     *
     * @var array
     */
    private $rate_limits = array();

    /**
     * Logger instance
     *
     * @var AI_CopyToolkit_Logger
     */
    private $logger;
    
    /**
     * Get instance
     *
     * @return AI_CopyToolkit_API
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $settings = AI_CopyToolkit::get_settings();
        $this->api_key = isset($settings['api_key']) ? trim($settings['api_key']) : '';

        add_action('wp_ajax_ai_copytoolkit_test_api', array($this, 'test_api_connection'));
        add_action('wp_ajax_ai_copytoolkit_get_models', array($this, 'get_available_models'));
        add_action('wp_ajax_ai_copytoolkit_debug_api', array($this, 'ajax_debug_connection'));

        // Initialize logger
        $this->logger = AI_CopyToolkit_Logger::instance();

        // Log API key status (without exposing the key)
        if (empty($this->api_key)) {
            $this->logger->warning('API key not configured');
        } else {
            $this->logger->info('API key configured', array(
                'key_length' => strlen($this->api_key),
                'key_prefix' => substr($this->api_key, 0, 8) . '...'
            ));
        }
    }
    
    /**
     * Test API connection
     */
    public function test_api_connection() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }

        $api_key = sanitize_text_field($_POST['api_key']);

        if (empty($api_key)) {
            wp_send_json_error(__('API key is required', 'ai-copytoolkit'));
        }

        // Test with a simple models request
        $response = $this->make_request('/models', array(), 'GET', $api_key);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $error_data = $response->get_error_data();

            // Provide more detailed error information
            $detailed_error = $error_message;
            if (is_array($error_data) && isset($error_data['status'])) {
                $detailed_error .= ' (HTTP ' . $error_data['status'] . ')';
            }

            $this->logger->error('API Connection Test Failed', array(
                'error' => $error_message,
                'error_data' => $error_data,
                'api_key_length' => strlen($api_key)
            ));

            wp_send_json_error($detailed_error);
        }

        // Check if we got valid models data
        if (!isset($response['data']) || !is_array($response['data'])) {
            wp_send_json_error(__('Invalid response format from OpenRouter API', 'ai-copytoolkit'));
        }

        $model_count = count($response['data']);

        $this->logger->info('API Connection Test Successful', array(
            'models_found' => $model_count,
            'api_key_length' => strlen($api_key)
        ));

        wp_send_json_success(sprintf(__('API connection successful! Found %d available models.', 'ai-copytoolkit'), $model_count));
    }
    
    /**
     * Get available models
     */
    public function get_available_models() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }

        // Refresh API key to ensure we have the latest
        $this->refresh_api_key();

        $force_refresh = isset($_POST['force_refresh']) && $_POST['force_refresh'];
        $models = $this->fetch_available_models($force_refresh);

        if (is_wp_error($models)) {
            $error_message = $models->get_error_message();
            $this->logger->error('Failed to fetch models', array(
                'error' => $error_message,
                'force_refresh' => $force_refresh
            ));
            wp_send_json_error($error_message);
        }

        wp_send_json_success($models);
    }
    
    /**
     * Fetch available models from OpenRouter
     *
     * @return array|WP_Error
     */
    public function fetch_available_models($force_refresh = false) {
        if (empty($this->api_key)) {
            return new WP_Error('no_api_key', __('OpenRouter API key not configured', 'ai-copytoolkit'));
        }

        $transient_key = 'ai_copytoolkit_models_' . md5($this->api_key);

        // Check cache first (unless force refresh)
        if (!$force_refresh) {
            $cached_models = get_transient($transient_key);
            if (false !== $cached_models) {
                return $cached_models;
            }
        }
        
        $response = $this->make_request('/models');
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $models = array();
        if (isset($response['data']) && is_array($response['data'])) {
            foreach ($response['data'] as $model) {
                $models[] = array(
                    'id' => $model['id'],
                    'name' => isset($model['name']) ? $model['name'] : $model['id'],
                    'description' => isset($model['description']) ? $model['description'] : '',
                    'pricing' => isset($model['pricing']) ? $model['pricing'] : array(),
                    'context_length' => isset($model['context_length']) ? $model['context_length'] : 4096,
                    'architecture' => isset($model['architecture']) ? $model['architecture'] : array(),
                );
            }
        }
        
        // Cache for 1 hour
        set_transient($transient_key, $models, HOUR_IN_SECONDS);
        
        return $models;
    }
    
    /**
     * Generate content using OpenRouter API
     *
     * @param string $prompt
     * @param array $parameters
     * @return array|WP_Error
     */
    public function generate_content($prompt, $parameters = array()) {
        // Refresh API key to ensure we have the latest
        $this->refresh_api_key();

        if (empty($this->api_key)) {
            return new WP_Error('no_api_key', __('OpenRouter API key not configured', 'ai-copytoolkit'));
        }
        
        if (empty($prompt)) {
            return new WP_Error('no_prompt', __('Prompt is required', 'ai-copytoolkit'));
        }
        
        // Check rate limits
        if (!$this->check_rate_limit()) {
            return new WP_Error('rate_limit', __('Rate limit exceeded. Please try again later.', 'ai-copytoolkit'));
        }
        
        $settings = AI_CopyToolkit::get_settings();
        
        $default_params = array(
            'model' => isset($settings['default_model']) ? $settings['default_model'] : 'openai/gpt-3.5-turbo',
            'max_tokens' => isset($settings['max_tokens']) ? intval($settings['max_tokens']) : 500,
            'temperature' => isset($settings['temperature']) ? floatval($settings['temperature']) : 0.7,
            'top_p' => 1,
            'frequency_penalty' => 0,
            'presence_penalty' => 0,
        );
        
        $params = wp_parse_args($parameters, $default_params);
        
        // Ensure model is properly formatted
        $model = $params['model'];
        if (empty($model)) {
            $model = 'openai/gpt-3.5-turbo';
        }

        $request_data = array(
            'model' => $model,
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => intval($params['max_tokens']),
            'temperature' => floatval($params['temperature']),
            'top_p' => floatval($params['top_p']),
            'frequency_penalty' => floatval($params['frequency_penalty']),
            'presence_penalty' => floatval($params['presence_penalty']),
        );

        // Remove any null or invalid values
        $request_data = array_filter($request_data, function($value) {
            return $value !== null && $value !== '';
        });
        
        $start_time = microtime(true);
        $response = $this->make_request('/chat/completions', $request_data);
        $generation_time = microtime(true) - $start_time;
        
        if (is_wp_error($response)) {
            $this->logger->error('API Request Failed', array(
                'error' => $response->get_error_message(),
                'prompt' => substr($prompt, 0, 100) . '...',
                'model' => $params['model'],
                'user_id' => get_current_user_id()
            ));
            return $response;
        }
        
        if (!isset($response['choices']) || empty($response['choices'])) {
            return new WP_Error('no_response', __('No response received from AI model', 'ai-copytoolkit'));
        }
        
        $generated_content = $response['choices'][0]['message']['content'];
        $tokens_used = isset($response['usage']['total_tokens']) ? $response['usage']['total_tokens'] : 0;
        
        // Update rate limits
        $this->update_rate_limit();
        
        // Track usage
        $this->track_usage($params['model'], $tokens_used);

        // Log successful generation
        $this->logger->log_api_request('/chat/completions', $request_data, $response, $generation_time);

        return array(
            'content' => $generated_content,
            'tokens_used' => $tokens_used,
            'model' => $params['model'],
            'generation_time' => $generation_time,
            'raw_response' => $response
        );
    }
    
    /**
     * Make API request to OpenRouter
     *
     * @param string $endpoint
     * @param array $data
     * @param string $method
     * @param string $api_key
     * @return array|WP_Error
     */
    private function make_request($endpoint, $data = array(), $method = 'POST', $api_key = null) {
        $api_key = $api_key ?: $this->api_key;

        if (empty($api_key)) {
            $this->logger->error('API Request Failed: No API key provided');
            return new WP_Error('no_api_key', __('API key is required', 'ai-copytoolkit'));
        }

        $url = $this->api_base_url . $endpoint;

        // OpenRouter specific headers
        $headers = array(
            'Authorization' => 'Bearer ' . trim($api_key),
            'Content-Type' => 'application/json',
            'HTTP-Referer' => home_url(),
            'X-Title' => get_bloginfo('name')
        );

        $args = array(
            'method' => $method,
            'headers' => $headers,
            'timeout' => 60,
            'user-agent' => 'AI-CopyToolkit/' . AI_COPYTOOLKIT_VERSION,
            'sslverify' => true,
            'blocking' => true
        );

        if ('POST' === $method && !empty($data)) {
            $args['body'] = wp_json_encode($data);
        } elseif ('GET' === $method && !empty($data)) {
            $url = add_query_arg($data, $url);
        }

        // Log the request for debugging
        $this->logger->info('Making API request', array(
            'url' => $url,
            'method' => $method,
            'headers' => array_merge($headers, array('Authorization' => 'Bearer ***')), // Hide API key
            'data_size' => strlen(wp_json_encode($data))
        ));

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            $this->logger->error('API Request Failed: WordPress HTTP Error', array(
                'error' => $response->get_error_message(),
                'url' => $url,
                'method' => $method
            ));
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        $response_headers = wp_remote_retrieve_headers($response);

        // Log response details
        $this->logger->info('API Response received', array(
            'status_code' => $response_code,
            'content_length' => strlen($response_body),
            'content_type' => isset($response_headers['content-type']) ? $response_headers['content-type'] : 'unknown'
        ));

        if ($response_code >= 400) {
            $error_data = json_decode($response_body, true);
            $error_message = 'API request failed';

            if (is_array($error_data)) {
                if (isset($error_data['error']['message'])) {
                    $error_message = $error_data['error']['message'];
                } elseif (isset($error_data['error'])) {
                    $error_message = is_string($error_data['error']) ? $error_data['error'] : wp_json_encode($error_data['error']);
                } elseif (isset($error_data['message'])) {
                    $error_message = $error_data['message'];
                }
            }

            $this->logger->error('API Request Failed: HTTP Error', array(
                'status_code' => $response_code,
                'error_message' => $error_message,
                'response_body' => substr($response_body, 0, 500),
                'url' => $url
            ));

            return new WP_Error('api_error', $error_message, array('status' => $response_code, 'body' => $response_body));
        }

        $decoded_response = json_decode($response_body, true);

        if (null === $decoded_response && json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->error('API Request Failed: Invalid JSON response', array(
                'json_error' => json_last_error_msg(),
                'response_body' => substr($response_body, 0, 500)
            ));
            return new WP_Error('invalid_response', __('Invalid JSON response from API', 'ai-copytoolkit'));
        }

        return $decoded_response;
    }

    /**
     * Refresh API key from settings
     */
    public function refresh_api_key() {
        $settings = AI_CopyToolkit::get_settings();
        $this->api_key = isset($settings['api_key']) ? trim($settings['api_key']) : '';

        $this->logger->info('API key refreshed', array(
            'key_length' => strlen($this->api_key),
            'key_prefix' => empty($this->api_key) ? 'empty' : substr($this->api_key, 0, 8) . '...'
        ));
    }

    /**
     * Check rate limit
     *
     * @return bool
     */
    private function check_rate_limit() {
        $user_id = get_current_user_id();
        $current_time = time();
        $rate_limit_key = 'ai_copytoolkit_rate_limit_' . $user_id;
        
        $rate_limit_data = get_transient($rate_limit_key);
        
        if (false === $rate_limit_data) {
            return true;
        }
        
        // Allow 60 requests per hour per user
        $max_requests = 60;
        $time_window = HOUR_IN_SECONDS;
        
        if ($rate_limit_data['count'] >= $max_requests && ($current_time - $rate_limit_data['start_time']) < $time_window) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Update rate limit
     */
    private function update_rate_limit() {
        $user_id = get_current_user_id();
        $current_time = time();
        $rate_limit_key = 'ai_copytoolkit_rate_limit_' . $user_id;
        
        $rate_limit_data = get_transient($rate_limit_key);
        
        if (false === $rate_limit_data || ($current_time - $rate_limit_data['start_time']) >= HOUR_IN_SECONDS) {
            $rate_limit_data = array(
                'count' => 1,
                'start_time' => $current_time
            );
        } else {
            $rate_limit_data['count']++;
        }
        
        set_transient($rate_limit_key, $rate_limit_data, HOUR_IN_SECONDS);
    }
    
    /**
     * Track API usage
     *
     * @param string $model
     * @param int $tokens_used
     */
    private function track_usage($model, $tokens_used) {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $table_name = AI_CopyToolkit_Database::get_table_name('api_usage');
        $request_date = current_time('Y-m-d');
        
        $wpdb->query($wpdb->prepare("
            INSERT INTO $table_name (user_id, model_used, tokens_used, request_date)
            VALUES (%d, %s, %d, %s)
            ON DUPLICATE KEY UPDATE tokens_used = tokens_used + %d
        ", $user_id, $model, $tokens_used, $request_date, $tokens_used));
    }

    /**
     * Debug API connection
     *
     * @return array Debug information
     */
    public function debug_connection() {
        $debug_info = array(
            'api_key_configured' => !empty($this->api_key),
            'api_key_length' => strlen($this->api_key),
            'api_key_prefix' => empty($this->api_key) ? 'none' : substr($this->api_key, 0, 8) . '...',
            'api_base_url' => $this->api_base_url,
            'wordpress_version' => get_bloginfo('version'),
            'php_version' => PHP_VERSION,
            'curl_available' => function_exists('curl_init'),
            'openssl_available' => function_exists('openssl_encrypt'),
            'wp_remote_get_available' => function_exists('wp_remote_get'),
            'site_url' => home_url(),
            'admin_url' => admin_url(),
            'current_user_can_edit' => current_user_can('edit_posts')
        );

        // Test basic connectivity
        $test_response = wp_remote_get('https://httpbin.org/get', array(
            'timeout' => 10,
            'user-agent' => 'AI-CopyToolkit-Debug/' . AI_COPYTOOLKIT_VERSION
        ));

        $debug_info['basic_connectivity'] = !is_wp_error($test_response);
        if (is_wp_error($test_response)) {
            $debug_info['connectivity_error'] = $test_response->get_error_message();
        } else {
            $debug_info['connectivity_status'] = wp_remote_retrieve_response_code($test_response);
        }

        // Test OpenRouter API specifically
        if (!empty($this->api_key)) {
            $openrouter_test = $this->make_request('/models', array(), 'GET');
            $debug_info['openrouter_api_test'] = !is_wp_error($openrouter_test);
            if (is_wp_error($openrouter_test)) {
                $debug_info['openrouter_error'] = $openrouter_test->get_error_message();
                $debug_info['openrouter_error_data'] = $openrouter_test->get_error_data();
            } else {
                $debug_info['openrouter_models_count'] = isset($openrouter_test['data']) ? count($openrouter_test['data']) : 0;
            }
        }

        return $debug_info;
    }

    /**
     * AJAX handler for debugging API connection
     */
    public function ajax_debug_connection() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }

        $debug_info = $this->debug_connection();
        wp_send_json_success($debug_info);
    }

}
