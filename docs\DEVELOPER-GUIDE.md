# AI CopyToolkit – Developer Guide

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Installation & Setup](#installation--setup)
3. [Core Classes](#core-classes)
4. [Database Schema](#database-schema)
5. [Hooks & Filters](#hooks--filters)
6. [API Integration](#api-integration)
7. [Security Implementation](#security-implementation)
8. [Extending the Plugin](#extending-the-plugin)
9. [Testing](#testing)

---

## Architecture Overview

AI CopyToolkit follows WordPress plugin best practices with a modular, object-oriented architecture:

```
ai-copytoolkit/
├── ai-copytoolkit.php          # Main plugin file
├── includes/                   # Core classes
│   ├── class-admin.php         # Admin interface
│   ├── class-api.php           # OpenRouter API integration
│   ├── class-content-generator.php # Content generation logic
│   ├── class-database.php      # Database management
│   ├── class-logger.php        # Logging system
│   └── class-security.php      # Security utilities
├── admin/                      # Admin interface files
│   └── pages/                  # Admin page templates
├── assets/                     # CSS, JS, images
├── languages/                  # Translation files
└── docs/                       # Documentation
```

### Design Patterns
- **Singleton Pattern**: Core classes use singleton pattern for single instances
- **Factory Pattern**: Template creation and management
- **Observer Pattern**: WordPress hooks and filters
- **Strategy Pattern**: Different AI models and content types

---

## Installation & Setup

### Requirements
- WordPress 5.0+
- PHP 7.4+
- MySQL 5.6+ or MariaDB 10.0+
- cURL extension
- OpenSSL extension

### Development Setup
1. Clone the repository to your WordPress plugins directory
2. Install dependencies (if any)
3. Activate the plugin
4. Configure your OpenRouter API key
5. Enable debug mode in wp-config.php

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

---

## Core Classes

### AI_CopyToolkit (Main Class)
The main plugin class that initializes all components.

```php
final class AI_CopyToolkit {
    private static $instance = null;
    
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function init_components() {
        AI_CopyToolkit_Security::instance();
        AI_CopyToolkit_Logger::instance();
        AI_CopyToolkit_Database::instance();
        // ... other components
    }
}
```

### AI_CopyToolkit_API
Handles all OpenRouter API communications.

**Key Methods:**
- `generate_content($prompt, $parameters)` - Generate content using AI
- `fetch_available_models()` - Get available AI models
- `test_api_connection()` - Verify API connectivity

### AI_CopyToolkit_Content_Generator
Manages content generation logic and templates.

**Key Methods:**
- `generate_content($template_id, $parameters, $custom_prompt)` - Main generation method
- `get_templates($category)` - Retrieve templates
- `save_template($template_data)` - Save custom templates

### AI_CopyToolkit_Database
Handles database operations and schema management.

**Key Methods:**
- `create_tables()` - Create plugin tables
- `get_table_name($table)` - Get prefixed table name
- `insert_default_templates()` - Populate default templates

### AI_CopyToolkit_Security
Provides security utilities and validation.

**Key Methods:**
- `sanitize_input($data, $type)` - Sanitize user input
- `validate_input($data, $type, $options)` - Validate data
- `check_rate_limit()` - Rate limiting implementation

### AI_CopyToolkit_Logger
Comprehensive logging system for debugging and monitoring.

**Key Methods:**
- `log($level, $message, $context)` - Log messages
- `get_recent_logs($limit, $level)` - Retrieve log entries
- `log_api_request($endpoint, $request, $response, $duration)` - Log API calls

---

## Database Schema

### Templates Table (`wp_ai_copytoolkit_templates`)
```sql
CREATE TABLE wp_ai_copytoolkit_templates (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    description text,
    category varchar(100) NOT NULL,
    content_type varchar(100) NOT NULL,
    prompt_template longtext NOT NULL,
    parameters longtext,
    is_active tinyint(1) DEFAULT 1,
    is_default tinyint(1) DEFAULT 0,
    created_by bigint(20) unsigned DEFAULT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
```

### History Table (`wp_ai_copytoolkit_history`)
```sql
CREATE TABLE wp_ai_copytoolkit_history (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    user_id bigint(20) unsigned NOT NULL,
    template_id bigint(20) unsigned DEFAULT NULL,
    model_used varchar(100) NOT NULL,
    prompt_text longtext NOT NULL,
    generated_content longtext NOT NULL,
    parameters longtext,
    tokens_used int(11) DEFAULT 0,
    generation_time float DEFAULT 0,
    status varchar(50) DEFAULT 'completed',
    error_message text,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
```

### API Usage Table (`wp_ai_copytoolkit_api_usage`)
```sql
CREATE TABLE wp_ai_copytoolkit_api_usage (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    user_id bigint(20) unsigned NOT NULL,
    model_used varchar(100) NOT NULL,
    tokens_used int(11) NOT NULL,
    cost decimal(10,6) DEFAULT 0.000000,
    request_date date NOT NULL,
    request_time datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
```

---

## Hooks & Filters

### Action Hooks

**Plugin Lifecycle:**
- `ai_copytoolkit_loaded` - Plugin fully initialized
- `ai_copytoolkit_activated` - Plugin activated
- `ai_copytoolkit_deactivated` - Plugin deactivated

**Content Generation:**
- `ai_copytoolkit_before_generation` - Before content generation
- `ai_copytoolkit_after_generation` - After content generation

### Filter Hooks

**Settings:**
```php
$settings = apply_filters('ai_copytoolkit_settings', $settings);
```

**Content Generation:**
```php
$prompt = apply_filters('ai_copytoolkit_before_generation', $prompt, $template_id, $parameters);
$result = apply_filters('ai_copytoolkit_after_generation', $result, $template_id, $parameters);
```

**Templates:**
```php
$templates = apply_filters('ai_copytoolkit_templates', $templates, $category);
```

### Usage Examples

**Modify Generation Parameters:**
```php
add_filter('ai_copytoolkit_before_generation', function($prompt, $template_id, $parameters) {
    // Add custom instructions to all prompts
    $prompt .= "\n\nPlease ensure the content is SEO-optimized.";
    return $prompt;
}, 10, 3);
```

**Custom Template Processing:**
```php
add_action('ai_copytoolkit_after_generation', function($result, $template_id, $parameters) {
    // Log successful generations
    error_log('Content generated: ' . strlen($result['content']) . ' characters');
}, 10, 3);
```

---

## API Integration

### OpenRouter Integration
The plugin integrates with OpenRouter's API to access multiple AI models.

**Supported Models:**
- OpenAI (GPT-3.5, GPT-4)
- Anthropic (Claude 3)
- Meta (LLaMA)
- Mistral AI
- And many more...

**API Request Structure:**
```php
$request_data = array(
    'model' => $model,
    'messages' => array(
        array(
            'role' => 'user',
            'content' => $prompt
        )
    ),
    'max_tokens' => $max_tokens,
    'temperature' => $temperature
);
```

**Error Handling:**
- Network timeouts
- API rate limits
- Invalid API keys
- Model availability
- Token limits

---

## Security Implementation

### Input Sanitization
All user inputs are sanitized using WordPress functions:

```php
$template_name = sanitize_text_field($_POST['template_name']);
$prompt = sanitize_textarea_field($_POST['prompt']);
$parameters = AI_CopyToolkit_Security::sanitize_input($_POST['parameters'], 'array');
```

### Nonce Verification
All AJAX requests require nonce verification:

```php
check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
```

### Capability Checks
User permissions are verified for all operations:

```php
if (!current_user_can('edit_posts')) {
    wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
}
```

### Rate Limiting
API requests are rate-limited per user and IP:

```php
// 60 requests per hour per user
// 100 requests per hour per IP
```

### Data Validation
Comprehensive input validation:

```php
$validation = AI_CopyToolkit_Security::validate_input($api_key, 'api_key');
if (is_wp_error($validation)) {
    return $validation;
}
```

---

## Extending the Plugin

### Adding Custom Content Types

1. **Register the Content Type:**
```php
add_filter('ai_copytoolkit_content_types', function($types) {
    $types['custom_type'] = __('Custom Content Type', 'textdomain');
    return $types;
});
```

2. **Create Custom Templates:**
```php
add_action('ai_copytoolkit_loaded', function() {
    // Add custom template logic
});
```

### Adding Custom AI Models

```php
add_filter('ai_copytoolkit_available_models', function($models) {
    $models[] = array(
        'id' => 'custom/model',
        'name' => 'Custom Model',
        'description' => 'Custom AI model integration'
    );
    return $models;
});
```

### Custom Template Parameters

```php
add_filter('ai_copytoolkit_template_parameters', function($parameters, $template_id) {
    if ($template_id === 'custom_template') {
        $parameters['custom_param'] = array(
            'type' => 'select',
            'options' => array('option1', 'option2'),
            'default' => 'option1',
            'label' => 'Custom Parameter'
        );
    }
    return $parameters;
}, 10, 2);
```

---

## Testing

### Unit Testing
The plugin includes PHPUnit tests for core functionality:

```bash
# Run all tests
phpunit

# Run specific test class
phpunit tests/TestContentGenerator.php
```

### Integration Testing
Test API integration with OpenRouter:

```php
public function test_api_connection() {
    $api = AI_CopyToolkit_API::instance();
    $result = $api->test_connection('valid_api_key');
    $this->assertTrue($result);
}
```

### Manual Testing Checklist
- [ ] Plugin activation/deactivation
- [ ] API key configuration
- [ ] Content generation with templates
- [ ] Custom prompt generation
- [ ] Template management
- [ ] History tracking
- [ ] Export functionality
- [ ] Error handling
- [ ] Security measures

### Performance Testing
- Monitor API response times
- Test with large content generations
- Verify database query efficiency
- Check memory usage

---

## Debugging

### Enable Debug Mode
```php
// In wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('AI_COPYTOOLKIT_DEBUG', true);
```

### Log Levels
- **Emergency**: System is unusable
- **Alert**: Action must be taken immediately
- **Critical**: Critical conditions
- **Error**: Error conditions
- **Warning**: Warning conditions
- **Notice**: Normal but significant condition
- **Info**: Informational messages
- **Debug**: Debug-level messages

### Common Debug Scenarios
1. API connection issues
2. Template parameter problems
3. Database query errors
4. Permission issues
5. Rate limiting problems

---

## Contributing

### Code Standards
- Follow WordPress Coding Standards
- Use PSR-4 autoloading
- Include comprehensive documentation
- Write unit tests for new features
- Maintain backward compatibility

### Pull Request Process
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Update documentation
6. Submit pull request

For more information, see the CONTRIBUTING.md file.
