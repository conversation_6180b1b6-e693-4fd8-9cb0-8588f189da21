<?php
/**
 * Database management class
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI_CopyToolkit_Database class
 */
class AI_CopyToolkit_Database {
    
    /**
     * Instance
     *
     * @var AI_CopyToolkit_Database
     */
    private static $instance = null;
    
    /**
     * Database version
     *
     * @var string
     */
    private static $db_version = '1.0.0';
    
    /**
     * Get instance
     *
     * @return AI_CopyToolkit_Database
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'check_database_version'));
    }
    
    /**
     * Check database version and update if needed
     */
    public function check_database_version() {
        $installed_version = get_option('ai_copytoolkit_db_version', '0.0.0');
        
        if (version_compare($installed_version, self::$db_version, '<')) {
            self::create_tables();
            update_option('ai_copytoolkit_db_version', self::$db_version);
        }
    }
    
    /**
     * Create database tables
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Templates table
        $templates_table = $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'templates';
        $templates_sql = "CREATE TABLE $templates_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            category varchar(100) NOT NULL,
            content_type varchar(100) NOT NULL,
            prompt_template longtext NOT NULL,
            parameters longtext,
            is_active tinyint(1) DEFAULT 1,
            is_default tinyint(1) DEFAULT 0,
            created_by bigint(20) unsigned DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY category (category),
            KEY content_type (content_type),
            KEY is_active (is_active),
            KEY created_by (created_by)
        ) $charset_collate;";
        
        // Generation history table
        $history_table = $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'history';
        $history_sql = "CREATE TABLE $history_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            template_id bigint(20) unsigned DEFAULT NULL,
            model_used varchar(100) NOT NULL,
            prompt_text longtext NOT NULL,
            generated_content longtext NOT NULL,
            parameters longtext,
            tokens_used int(11) DEFAULT 0,
            generation_time float DEFAULT 0,
            status varchar(50) DEFAULT 'completed',
            error_message text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY template_id (template_id),
            KEY model_used (model_used),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // API usage tracking table
        $usage_table = $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'api_usage';
        $usage_sql = "CREATE TABLE $usage_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            model_used varchar(100) NOT NULL,
            tokens_used int(11) NOT NULL,
            cost decimal(10,6) DEFAULT 0.000000,
            request_date date NOT NULL,
            request_time datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY model_used (model_used),
            KEY request_date (request_date),
            UNIQUE KEY unique_user_model_date (user_id, model_used, request_date)
        ) $charset_collate;";
        
        // Settings table for user-specific settings
        $user_settings_table = $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'user_settings';
        $user_settings_sql = "CREATE TABLE $user_settings_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            setting_key varchar(255) NOT NULL,
            setting_value longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_user_setting (user_id, setting_key),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($templates_sql);
        dbDelta($history_sql);
        dbDelta($usage_sql);
        dbDelta($user_settings_sql);
        
        // Insert default templates
        self::insert_default_templates();
    }
    
    /**
     * Drop database tables
     */
    public static function drop_tables() {
        global $wpdb;
        
        $tables = array(
            $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'templates',
            $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'history',
            $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'api_usage',
            $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'user_settings'
        );
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
        
        delete_option('ai_copytoolkit_db_version');
    }
    
    /**
     * Insert default templates
     */
    private static function insert_default_templates() {
        global $wpdb;
        
        $templates_table = $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'templates';
        
        // Check if templates already exist
        $existing = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_default = 1");
        if ($existing > 0) {
            return;
        }
        
        $default_templates = array(
            array(
                'name' => 'Product Description - E-commerce',
                'description' => 'Generate compelling product descriptions for e-commerce stores',
                'category' => 'ecommerce',
                'content_type' => 'product_description',
                'prompt_template' => 'Write a compelling product description for: {product_name}

Product details:
- Features: {features}
- Benefits: {benefits}
- Target audience: {target_audience}
- Tone: {tone}

Create a description that highlights the key benefits, addresses customer pain points, and includes a compelling call-to-action. Keep it between {min_words}-{max_words} words.',
                'parameters' => json_encode(array(
                    'product_name' => array('type' => 'text', 'required' => true, 'label' => 'Product Name'),
                    'features' => array('type' => 'textarea', 'required' => true, 'label' => 'Key Features'),
                    'benefits' => array('type' => 'textarea', 'required' => true, 'label' => 'Main Benefits'),
                    'target_audience' => array('type' => 'text', 'required' => true, 'label' => 'Target Audience'),
                    'tone' => array('type' => 'select', 'options' => array('professional', 'casual', 'enthusiastic', 'luxury'), 'default' => 'professional', 'label' => 'Tone'),
                    'min_words' => array('type' => 'number', 'default' => 100, 'label' => 'Minimum Words'),
                    'max_words' => array('type' => 'number', 'default' => 200, 'label' => 'Maximum Words')
                )),
                'is_default' => 1
            ),
            array(
                'name' => 'Facebook Ad Copy',
                'description' => 'Create engaging Facebook ad copy that converts',
                'category' => 'advertising',
                'content_type' => 'facebook_ad',
                'prompt_template' => 'Create a high-converting Facebook ad copy for: {product_service}

Target audience: {target_audience}
Main benefit/value proposition: {value_proposition}
Call-to-action: {cta_type}
Tone: {tone}
Ad objective: {objective}

Include:
- Attention-grabbing headline
- Compelling body text (2-3 sentences)
- Strong call-to-action
- Use emojis where appropriate
- Keep total length under 150 words',
                'parameters' => json_encode(array(
                    'product_service' => array('type' => 'text', 'required' => true, 'label' => 'Product/Service'),
                    'target_audience' => array('type' => 'text', 'required' => true, 'label' => 'Target Audience'),
                    'value_proposition' => array('type' => 'textarea', 'required' => true, 'label' => 'Value Proposition'),
                    'cta_type' => array('type' => 'select', 'options' => array('Learn More', 'Shop Now', 'Sign Up', 'Download', 'Get Quote'), 'default' => 'Learn More', 'label' => 'Call-to-Action'),
                    'tone' => array('type' => 'select', 'options' => array('urgent', 'friendly', 'professional', 'exciting'), 'default' => 'friendly', 'label' => 'Tone'),
                    'objective' => array('type' => 'select', 'options' => array('awareness', 'traffic', 'conversions', 'leads'), 'default' => 'conversions', 'label' => 'Campaign Objective')
                )),
                'is_default' => 1
            ),
            array(
                'name' => 'Email Subject Lines',
                'description' => 'Generate compelling email subject lines that increase open rates',
                'category' => 'email_marketing',
                'content_type' => 'email_subject',
                'prompt_template' => 'Generate 5 compelling email subject lines for: {email_topic}

Email type: {email_type}
Target audience: {target_audience}
Tone: {tone}
Goal: {goal}

Requirements:
- Keep under 50 characters for mobile optimization
- Create urgency or curiosity
- Avoid spam trigger words
- Include personalization where appropriate
- Make them action-oriented',
                'parameters' => json_encode(array(
                    'email_topic' => array('type' => 'text', 'required' => true, 'label' => 'Email Topic/Content'),
                    'email_type' => array('type' => 'select', 'options' => array('newsletter', 'promotional', 'welcome', 'abandoned_cart', 'follow_up'), 'default' => 'promotional', 'label' => 'Email Type'),
                    'target_audience' => array('type' => 'text', 'required' => true, 'label' => 'Target Audience'),
                    'tone' => array('type' => 'select', 'options' => array('urgent', 'friendly', 'professional', 'playful', 'exclusive'), 'default' => 'friendly', 'label' => 'Tone'),
                    'goal' => array('type' => 'select', 'options' => array('increase_opens', 'drive_clicks', 'build_awareness', 'promote_sale'), 'default' => 'increase_opens', 'label' => 'Primary Goal')
                )),
                'is_default' => 1
            ),
            array(
                'name' => 'Call-to-Action Buttons',
                'description' => 'Create compelling CTA copy that drives conversions',
                'category' => 'conversion',
                'content_type' => 'cta',
                'prompt_template' => 'Create 5 high-converting call-to-action button texts for: {offer_description}

Context: {context}
Target action: {target_action}
Audience: {target_audience}
Urgency level: {urgency}
Tone: {tone}

Requirements:
- Keep under 5 words per CTA
- Create action-oriented language
- Include benefit or value
- Consider psychological triggers
- Avoid generic phrases like "Click Here"',
                'parameters' => json_encode(array(
                    'offer_description' => array('type' => 'textarea', 'required' => true, 'label' => 'Offer/Product Description'),
                    'context' => array('type' => 'select', 'options' => array('website_button', 'email_button', 'ad_button', 'popup', 'landing_page'), 'default' => 'website_button', 'label' => 'Where will this CTA appear?'),
                    'target_action' => array('type' => 'select', 'options' => array('purchase', 'signup', 'download', 'learn_more', 'contact'), 'default' => 'purchase', 'label' => 'Desired Action'),
                    'target_audience' => array('type' => 'text', 'required' => true, 'label' => 'Target Audience'),
                    'urgency' => array('type' => 'select', 'options' => array('high', 'medium', 'low'), 'default' => 'medium', 'label' => 'Urgency Level'),
                    'tone' => array('type' => 'select', 'options' => array('direct', 'friendly', 'professional', 'exciting'), 'default' => 'direct', 'label' => 'Tone')
                )),
                'is_default' => 1
            ),
            array(
                'name' => 'Social Media Posts',
                'description' => 'Generate engaging social media content for various platforms',
                'category' => 'social_media',
                'content_type' => 'social_post',
                'prompt_template' => 'Create an engaging social media post for: {topic}

Platform: {platform}
Post type: {post_type}
Target audience: {target_audience}
Tone: {tone}
Include hashtags: {include_hashtags}
Call-to-action: {include_cta}

Requirements:
- Optimize for {platform} best practices
- Include relevant emojis
- Create engaging hook in first line
- Keep within platform character limits
- Encourage engagement (likes, comments, shares)',
                'parameters' => json_encode(array(
                    'topic' => array('type' => 'textarea', 'required' => true, 'label' => 'Post Topic/Content'),
                    'platform' => array('type' => 'select', 'options' => array('facebook', 'instagram', 'twitter', 'linkedin', 'tiktok'), 'default' => 'facebook', 'label' => 'Social Platform'),
                    'post_type' => array('type' => 'select', 'options' => array('promotional', 'educational', 'entertaining', 'inspirational', 'behind_scenes'), 'default' => 'promotional', 'label' => 'Post Type'),
                    'target_audience' => array('type' => 'text', 'required' => true, 'label' => 'Target Audience'),
                    'tone' => array('type' => 'select', 'options' => array('casual', 'professional', 'humorous', 'inspirational', 'urgent'), 'default' => 'casual', 'label' => 'Tone'),
                    'include_hashtags' => array('type' => 'select', 'options' => array('yes', 'no'), 'default' => 'yes', 'label' => 'Include Hashtags'),
                    'include_cta' => array('type' => 'select', 'options' => array('yes', 'no'), 'default' => 'yes', 'label' => 'Include Call-to-Action')
                )),
                'is_default' => 1
            )
        );
        
        foreach ($default_templates as $template) {
            $wpdb->insert($templates_table, $template);
        }
    }
    
    /**
     * Get table name with prefix
     *
     * @param string $table_name
     * @return string
     */
    public static function get_table_name($table_name) {
        global $wpdb;
        return $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . $table_name;
    }
}
