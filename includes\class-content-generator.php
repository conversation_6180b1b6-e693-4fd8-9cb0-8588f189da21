<?php
/**
 * Content generation class
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI_CopyToolkit_Content_Generator class
 */
class AI_CopyToolkit_Content_Generator {
    
    /**
     * Instance
     *
     * @var AI_CopyToolkit_Content_Generator
     */
    private static $instance = null;
    
    /**
     * API instance
     *
     * @var AI_CopyToolkit_API
     */
    private $api;

    /**
     * Logger instance
     *
     * @var AI_CopyToolkit_Logger
     */
    private $logger;
    
    /**
     * Get instance
     *
     * @return AI_CopyToolkit_Content_Generator
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->api = AI_CopyToolkit_API::instance();
        $this->logger = AI_CopyToolkit_Logger::instance();

        add_action('wp_ajax_ai_copytoolkit_generate_content', array($this, 'ajax_generate_content'));
        add_action('wp_ajax_ai_copytoolkit_get_templates', array($this, 'ajax_get_templates'));
        add_action('wp_ajax_ai_copytoolkit_get_template_parameters', array($this, 'ajax_get_template_parameters'));
        add_action('wp_ajax_ai_copytoolkit_save_template', array($this, 'ajax_save_template'));
        add_action('wp_ajax_ai_copytoolkit_delete_template', array($this, 'ajax_delete_template'));
    }
    
    /**
     * AJAX handler for content generation
     */
    public function ajax_generate_content() {
        try {
            check_ajax_referer('ai_copytoolkit_nonce', 'nonce');

            if (!current_user_can('edit_posts')) {
                $this->logger->warning('Unauthorized content generation attempt', array(
                    'user_id' => get_current_user_id(),
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ));
                wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
            }

            // Validate and sanitize inputs
            $template_id = intval($_POST['template_id']);
            $parameters = isset($_POST['parameters']) ? $_POST['parameters'] : array();
            $template_parameters = isset($_POST['template_parameters']) ? $_POST['template_parameters'] : array();
            $custom_prompt = isset($_POST['custom_prompt']) ? sanitize_textarea_field($_POST['custom_prompt']) : '';

            // Sanitize template parameters
            $template_parameters = AI_CopyToolkit_Security::sanitize_input($template_parameters, 'array');

            if (empty($template_id) && empty($custom_prompt)) {
                $this->logger->warning('Content generation attempted without template or prompt', array(
                    'user_id' => get_current_user_id()
                ));
                wp_send_json_error(__('Template or custom prompt is required', 'ai-copytoolkit'));
            }

            $this->logger->info('Content generation started', array(
                'user_id' => get_current_user_id(),
                'template_id' => $template_id,
                'has_custom_prompt' => !empty($custom_prompt)
            ));

            $result = $this->generate_content($template_id, $parameters, $custom_prompt, $template_parameters);

            if (is_wp_error($result)) {
                $this->logger->error('Content generation failed', array(
                    'user_id' => get_current_user_id(),
                    'template_id' => $template_id,
                    'error' => $result->get_error_message()
                ));
                wp_send_json_error($result->get_error_message());
            }

            $this->logger->info('Content generation completed successfully', array(
                'user_id' => get_current_user_id(),
                'template_id' => $template_id,
                'tokens_used' => $result['tokens_used'] ?? 0,
                'generation_time' => $result['generation_time'] ?? 0
            ));

            wp_send_json_success($result);

        } catch (Exception $e) {
            $this->logger->critical('Unexpected error in content generation', array(
                'user_id' => get_current_user_id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));
            wp_send_json_error(__('An unexpected error occurred. Please try again.', 'ai-copytoolkit'));
        }
    }
    
    /**
     * Generate content
     *
     * @param int $template_id
     * @param array $parameters
     * @param string $custom_prompt
     * @param array $template_parameters
     * @return array|WP_Error
     */
    public function generate_content($template_id = 0, $parameters = array(), $custom_prompt = '', $template_parameters = array()) {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $prompt = '';
        
        if (!empty($custom_prompt)) {
            $prompt = $custom_prompt;
        } elseif ($template_id > 0) {
            $template = $this->get_template($template_id);
            
            if (!$template) {
                return new WP_Error('template_not_found', __('Template not found', 'ai-copytoolkit'));
            }
            
            $prompt = $this->build_prompt_from_template($template, $template_parameters);
        }
        
        if (empty($prompt)) {
            return new WP_Error('empty_prompt', __('Unable to generate prompt', 'ai-copytoolkit'));
        }
        
        // Apply filters to allow customization
        $prompt = apply_filters('ai_copytoolkit_before_generation', $prompt, $template_id, $parameters);
        
        $generation_params = array();
        if (isset($parameters['model'])) {
            $generation_params['model'] = sanitize_text_field($parameters['model']);
        }
        if (isset($parameters['max_tokens'])) {
            $generation_params['max_tokens'] = intval($parameters['max_tokens']);
        }
        if (isset($parameters['temperature'])) {
            $generation_params['temperature'] = floatval($parameters['temperature']);
        }
        
        $result = $this->api->generate_content($prompt, $generation_params);
        
        if (is_wp_error($result)) {
            // Log failed generation
            $this->save_generation_history($user_id, $template_id, $prompt, '', $generation_params, 0, 0, 'failed', $result->get_error_message());
            return $result;
        }
        
        // Save successful generation to history
        $history_id = $this->save_generation_history(
            $user_id,
            $template_id,
            $prompt,
            $result['content'],
            $generation_params,
            $result['tokens_used'],
            $result['generation_time'],
            'completed'
        );
        
        $result['history_id'] = $history_id;
        
        // Apply filters after generation
        $result = apply_filters('ai_copytoolkit_after_generation', $result, $template_id, $parameters);
        
        return $result;
    }
    
    /**
     * Build prompt from template
     *
     * @param object $template
     * @param array $parameters
     * @return string
     */
    private function build_prompt_from_template($template, $parameters) {
        $prompt = $template->prompt_template;
        $template_params = json_decode($template->parameters, true);
        
        if (!is_array($template_params)) {
            return $prompt;
        }
        
        foreach ($template_params as $param_key => $param_config) {
            $placeholder = '{' . $param_key . '}';
            $value = '';
            
            if (isset($parameters[$param_key])) {
                $value = sanitize_textarea_field($parameters[$param_key]);
            } elseif (isset($param_config['default'])) {
                $value = $param_config['default'];
            }
            
            $prompt = str_replace($placeholder, $value, $prompt);
        }
        
        return $prompt;
    }
    
    /**
     * Get template by ID
     *
     * @param int $template_id
     * @return object|null
     */
    public function get_template($template_id) {
        global $wpdb;
        
        $table_name = AI_CopyToolkit_Database::get_table_name('templates');
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND is_active = 1",
            $template_id
        ));
    }
    
    /**
     * Get all templates
     *
     * @param string $category
     * @return array
     */
    public function get_templates($category = '') {
        global $wpdb;
        
        $table_name = AI_CopyToolkit_Database::get_table_name('templates');
        $where_clause = "WHERE is_active = 1";
        $params = array();
        
        if (!empty($category)) {
            $where_clause .= " AND category = %s";
            $params[] = $category;
        }
        
        $query = "SELECT * FROM $table_name $where_clause ORDER BY is_default DESC, name ASC";
        
        if (!empty($params)) {
            return $wpdb->get_results($wpdb->prepare($query, $params));
        }
        
        return $wpdb->get_results($query);
    }
    
    /**
     * AJAX handler for getting templates
     */
    public function ajax_get_templates() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }

        $category = isset($_POST['category']) ? sanitize_text_field($_POST['category']) : '';
        $templates = $this->get_templates($category);

        wp_send_json_success($templates);
    }

    /**
     * AJAX handler for getting template parameters
     */
    public function ajax_get_template_parameters() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }

        $template_id = intval($_POST['template_id']);
        $template = $this->get_template($template_id);

        if (!$template) {
            wp_send_json_error(__('Template not found', 'ai-copytoolkit'));
        }

        $parameters = json_decode($template->parameters, true);
        if (!is_array($parameters)) {
            $parameters = array();
        }

        wp_send_json_success(array(
            'template' => $template,
            'parameters' => $parameters
        ));
    }
    
    /**
     * Save template
     *
     * @param array $template_data
     * @return int|WP_Error
     */
    public function save_template($template_data) {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $table_name = AI_CopyToolkit_Database::get_table_name('templates');
        
        $data = array(
            'name' => sanitize_text_field($template_data['name']),
            'description' => sanitize_textarea_field($template_data['description']),
            'category' => sanitize_text_field($template_data['category']),
            'content_type' => sanitize_text_field($template_data['content_type']),
            'prompt_template' => sanitize_textarea_field($template_data['prompt_template']),
            'parameters' => wp_json_encode($template_data['parameters']),
            'created_by' => $user_id,
        );
        
        if (isset($template_data['id']) && $template_data['id'] > 0) {
            // Update existing template
            $template_id = intval($template_data['id']);
            
            // Check if user can edit this template
            $existing_template = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $table_name WHERE id = %d",
                $template_id
            ));
            
            if (!$existing_template) {
                return new WP_Error('template_not_found', __('Template not found', 'ai-copytoolkit'));
            }
            
            if ($existing_template->created_by != $user_id && !current_user_can('manage_options')) {
                return new WP_Error('insufficient_permissions', __('You can only edit your own templates', 'ai-copytoolkit'));
            }
            
            $result = $wpdb->update($table_name, $data, array('id' => $template_id));
            
            if (false === $result) {
                return new WP_Error('update_failed', __('Failed to update template', 'ai-copytoolkit'));
            }
            
            return $template_id;
        } else {
            // Create new template
            $result = $wpdb->insert($table_name, $data);
            
            if (false === $result) {
                return new WP_Error('insert_failed', __('Failed to create template', 'ai-copytoolkit'));
            }
            
            return $wpdb->insert_id;
        }
    }
    
    /**
     * AJAX handler for saving template
     */
    public function ajax_save_template() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        $template_data = $_POST['template'];
        $result = $this->save_template($template_data);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        wp_send_json_success(array('template_id' => $result));
    }
    
    /**
     * Delete template
     *
     * @param int $template_id
     * @return bool|WP_Error
     */
    public function delete_template($template_id) {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $table_name = AI_CopyToolkit_Database::get_table_name('templates');
        
        $template = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $template_id
        ));
        
        if (!$template) {
            return new WP_Error('template_not_found', __('Template not found', 'ai-copytoolkit'));
        }
        
        // Don't allow deletion of default templates
        if ($template->is_default) {
            return new WP_Error('cannot_delete_default', __('Cannot delete default templates', 'ai-copytoolkit'));
        }
        
        // Check permissions
        if ($template->created_by != $user_id && !current_user_can('manage_options')) {
            return new WP_Error('insufficient_permissions', __('You can only delete your own templates', 'ai-copytoolkit'));
        }
        
        $result = $wpdb->update(
            $table_name,
            array('is_active' => 0),
            array('id' => $template_id)
        );
        
        if (false === $result) {
            return new WP_Error('delete_failed', __('Failed to delete template', 'ai-copytoolkit'));
        }
        
        return true;
    }
    
    /**
     * AJAX handler for deleting template
     */
    public function ajax_delete_template() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        $template_id = intval($_POST['template_id']);
        $result = $this->delete_template($template_id);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        wp_send_json_success();
    }
    
    /**
     * Save generation history
     *
     * @param int $user_id
     * @param int $template_id
     * @param string $prompt
     * @param string $content
     * @param array $parameters
     * @param int $tokens_used
     * @param float $generation_time
     * @param string $status
     * @param string $error_message
     * @return int|false
     */
    private function save_generation_history($user_id, $template_id, $prompt, $content, $parameters, $tokens_used, $generation_time, $status = 'completed', $error_message = '') {
        global $wpdb;
        
        $settings = AI_CopyToolkit::get_settings();
        
        // Check if history is enabled
        if (!isset($settings['enable_history']) || !$settings['enable_history']) {
            return false;
        }
        
        $table_name = AI_CopyToolkit_Database::get_table_name('history');
        
        $data = array(
            'user_id' => $user_id,
            'template_id' => $template_id > 0 ? $template_id : null,
            'model_used' => isset($parameters['model']) ? $parameters['model'] : 'unknown',
            'prompt_text' => $prompt,
            'generated_content' => $content,
            'parameters' => wp_json_encode($parameters),
            'tokens_used' => $tokens_used,
            'generation_time' => $generation_time,
            'status' => $status,
            'error_message' => $error_message,
        );
        
        $result = $wpdb->insert($table_name, $data);
        
        if (false === $result) {
            return false;
        }
        
        // Clean up old history entries if limit is set
        $this->cleanup_history($user_id);
        
        return $wpdb->insert_id;
    }
    
    /**
     * Clean up old history entries
     *
     * @param int $user_id
     */
    private function cleanup_history($user_id) {
        global $wpdb;
        
        $settings = AI_CopyToolkit::get_settings();
        $history_limit = isset($settings['history_limit']) ? intval($settings['history_limit']) : 100;
        
        if ($history_limit <= 0) {
            return;
        }
        
        $table_name = AI_CopyToolkit_Database::get_table_name('history');
        
        $wpdb->query($wpdb->prepare("
            DELETE FROM $table_name 
            WHERE user_id = %d 
            AND id NOT IN (
                SELECT id FROM (
                    SELECT id FROM $table_name 
                    WHERE user_id = %d 
                    ORDER BY created_at DESC 
                    LIMIT %d
                ) AS recent_history
            )
        ", $user_id, $user_id, $history_limit));
    }
}
