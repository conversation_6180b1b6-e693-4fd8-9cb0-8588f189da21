# AI CopyToolkit Installation Guide

## System Requirements

### Minimum Requirements
- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher
- **MySQL**: 5.6 or higher (or MariaDB 10.0+)
- **Memory**: 128MB PHP memory limit (256MB recommended)
- **Disk Space**: 10MB free space

### Recommended Requirements
- **WordPress**: 6.0 or higher
- **PHP**: 8.0 or higher
- **MySQL**: 8.0 or higher
- **Memory**: 512MB PHP memory limit
- **SSL**: HTTPS enabled (required for secure API communication)

### Required PHP Extensions
- **cURL**: For API communication with OpenRouter
- **OpenSSL**: For secure HTTPS connections
- **JSON**: For data processing
- **mbstring**: For text processing

### Server Configuration
- **allow_url_fopen**: Enabled
- **file_uploads**: Enabled
- **max_execution_time**: 300 seconds (for large content generation)
- **max_input_vars**: 3000 or higher

---

## Installation Methods

### Method 1: WordPress Admin Upload (Recommended)

1. **Download the Plugin**
   - Download the `ai-copytoolkit.zip` file from your purchase confirmation email
   - Save it to your computer

2. **Upload via WordPress Admin**
   - Log in to your WordPress admin dashboard
   - Navigate to **Plugins > Add New**
   - Click **Upload Plugin**
   - Choose the `ai-copytoolkit.zip` file
   - Click **Install Now**

3. **Activate the Plugin**
   - Click **Activate Plugin** after installation completes
   - You'll see "AI CopyToolkit" appear in your admin menu

### Method 2: FTP Upload

1. **Extract the Plugin**
   - Unzip the `ai-copytoolkit.zip` file on your computer
   - You should see an `ai-copytoolkit` folder

2. **Upload via FTP**
   - Connect to your website via FTP
   - Navigate to `/wp-content/plugins/`
   - Upload the entire `ai-copytoolkit` folder

3. **Activate via WordPress Admin**
   - Log in to your WordPress admin
   - Go to **Plugins > Installed Plugins**
   - Find "AI CopyToolkit" and click **Activate**

### Method 3: WP-CLI Installation

```bash
# Upload the plugin file to your server first, then:
wp plugin install /path/to/ai-copytoolkit.zip --activate
```

---

## Initial Configuration

### Step 1: Access the Plugin
After activation, you'll find "AI CopyToolkit" in your WordPress admin menu. Click it to access the dashboard.

### Step 2: Configure API Settings

1. **Get Your OpenRouter API Key**
   - Visit [OpenRouter.ai](https://openrouter.ai/keys)
   - Sign up for a free account
   - Generate your API key
   - Copy the key to your clipboard

2. **Enter API Key in Plugin**
   - Go to **AI CopyToolkit > Settings**
   - Paste your API key in the "OpenRouter API Key" field
   - Click **Test Connection** to verify it works
   - You should see a "Connection successful" message

3. **Configure Default Settings**
   - **Default AI Model**: Choose `openai/gpt-3.5-turbo` for beginners
   - **Max Tokens**: Set to `500` (good starting point)
   - **Temperature**: Set to `0.7` (balanced creativity)
   - **Enable History**: Check this box to save your generations
   - **History Limit**: Set to `100` entries

4. **Save Settings**
   - Click **Save Settings** to store your configuration

### Step 3: Test Content Generation

1. **Navigate to Content Generator**
   - Go to **AI CopyToolkit > Generate Content**

2. **Try a Template**
   - Click on "Product Description - E-commerce" template
   - Fill in the parameters:
     - Product Name: "Wireless Bluetooth Headphones"
     - Features: "Noise cancellation, 30-hour battery, comfortable fit"
     - Benefits: "Enjoy music without distractions"
     - Target Audience: "Music lovers and commuters"
     - Tone: "Professional"

3. **Generate Content**
   - Click **Generate Content**
   - Wait for the AI to create your content
   - Review the generated text

If this works, your installation is complete!

---

## Troubleshooting Installation Issues

### Common Installation Problems

**Problem**: "Plugin could not be activated because it triggered a fatal error"
**Solution**: 
- Check PHP version (must be 7.4+)
- Increase PHP memory limit to 256MB
- Check error logs for specific issues

**Problem**: "The uploaded file exceeds the upload_max_filesize directive"
**Solution**:
- Increase `upload_max_filesize` in php.ini
- Or use FTP installation method instead

**Problem**: "Installation failed: Destination folder already exists"
**Solution**:
- Delete the existing `ai-copytoolkit` folder via FTP
- Try installation again

### API Configuration Issues

**Problem**: "API key not configured" error
**Solution**:
- Verify you've entered the correct API key
- Check for extra spaces or characters
- Ensure your OpenRouter account is active

**Problem**: "Connection test failed"
**Solution**:
- Check your server's internet connectivity
- Verify cURL is installed and enabled
- Check if your server blocks outgoing HTTPS requests
- Contact your hosting provider if needed

**Problem**: "Rate limit exceeded"
**Solution**:
- Wait 1 hour before trying again
- Check your OpenRouter usage limits
- Consider upgrading your OpenRouter plan

### Permission Issues

**Problem**: "Insufficient permissions" errors
**Solution**:
- Ensure your user account has appropriate WordPress capabilities
- Content generation requires `edit_posts` capability
- Settings require `manage_options` capability
- Contact your site administrator if needed

---

## Server Configuration

### PHP Configuration
Add these settings to your `php.ini` file:

```ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 10M
post_max_size = 10M
max_input_vars = 3000
allow_url_fopen = On
```

### WordPress Configuration
Add these constants to your `wp-config.php` file:

```php
// Increase memory limit
define('WP_MEMORY_LIMIT', '256M');

// Enable debug mode (for troubleshooting)
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### .htaccess Configuration
Ensure your `.htaccess` file allows the plugin to function:

```apache
# WordPress default rules
RewriteEngine On
RewriteBase /
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.php [L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options SAMEORIGIN
Header always set X-XSS-Protection "1; mode=block"
```

---

## Database Setup

The plugin automatically creates the following database tables:

- `wp_ai_copytoolkit_templates` - Content templates
- `wp_ai_copytoolkit_history` - Generation history
- `wp_ai_copytoolkit_api_usage` - API usage tracking
- `wp_ai_copytoolkit_user_settings` - User-specific settings

### Manual Database Creation
If automatic table creation fails, you can create them manually:

```sql
-- Run these queries in phpMyAdmin or similar tool
-- Replace 'wp_' with your actual WordPress table prefix

CREATE TABLE wp_ai_copytoolkit_templates (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    description text,
    category varchar(100) NOT NULL,
    content_type varchar(100) NOT NULL,
    prompt_template longtext NOT NULL,
    parameters longtext,
    is_active tinyint(1) DEFAULT 1,
    is_default tinyint(1) DEFAULT 0,
    created_by bigint(20) unsigned DEFAULT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

-- Additional tables... (see DEVELOPER-GUIDE.md for complete schema)
```

---

## Multisite Installation

### Network Activation
For WordPress multisite networks:

1. **Upload Plugin**
   - Upload to `/wp-content/plugins/` as normal

2. **Network Activate**
   - Go to **Network Admin > Plugins**
   - Click **Network Activate** for AI CopyToolkit

3. **Configure Per Site**
   - Each site needs its own API key configuration
   - Settings are not shared between sites
   - Usage tracking is per-site

### Site-Specific Installation
Alternatively, activate on individual sites:

1. **Upload Plugin** to main plugins directory
2. **Activate Per Site** via each site's admin panel
3. **Configure Individually** for each site

---

## Security Considerations

### File Permissions
Set appropriate file permissions:

```bash
# Plugin directory
chmod 755 /wp-content/plugins/ai-copytoolkit/

# Plugin files
find /wp-content/plugins/ai-copytoolkit/ -type f -exec chmod 644 {} \;

# Make sure wp-config.php is secure
chmod 600 wp-config.php
```

### API Key Security
- Never share your API key publicly
- Use environment variables for API keys when possible
- Regularly rotate your API keys
- Monitor usage for unauthorized access

### WordPress Security
- Keep WordPress core updated
- Use strong admin passwords
- Install security plugins
- Enable two-factor authentication
- Regular security scans

---

## Performance Optimization

### Caching
The plugin is compatible with popular caching plugins:
- WP Rocket
- W3 Total Cache
- WP Super Cache
- LiteSpeed Cache

### Database Optimization
- Regular database cleanup
- Optimize database tables monthly
- Monitor database size growth
- Set appropriate history limits

### Server Optimization
- Use PHP 8.0+ for better performance
- Enable OPcache
- Use SSD storage
- Adequate RAM allocation

---

## Backup and Migration

### Before Installation
- **Backup your website** completely
- **Export your database**
- **Test on staging site** first

### After Installation
- **Backup plugin settings** regularly
- **Export generation history** periodically
- **Document custom templates**

### Migration Process
1. **Export Settings** from old site
2. **Install Plugin** on new site
3. **Import Settings** and templates
4. **Test Functionality** thoroughly

---

## Getting Help

### Self-Help Resources
- Check the **User Guide** for usage instructions
- Review **System Logs** in the plugin admin
- Test with different AI models and settings
- Verify server requirements are met

### Support Channels
- **Documentation**: Complete guides and tutorials
- **Support Forum**: Community help and discussions
- **Email Support**: Direct technical assistance
- **Video Tutorials**: Step-by-step walkthroughs

### Before Contacting Support
Please gather this information:
- WordPress version
- PHP version
- Plugin version
- Error messages (exact text)
- Steps to reproduce the issue
- Server configuration details

---

## Next Steps

After successful installation:

1. **Read the User Guide** to learn all features
2. **Explore Templates** to understand content types
3. **Create Custom Templates** for your specific needs
4. **Set Usage Limits** to control costs
5. **Train Your Team** on best practices
6. **Monitor Performance** and optimize settings

Congratulations! You're ready to start generating amazing content with AI CopyToolkit.
