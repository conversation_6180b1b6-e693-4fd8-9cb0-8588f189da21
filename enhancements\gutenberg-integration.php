<?php
/**
 * <PERSON><PERSON><PERSON> Block Integration for AI CopyToolkit
 * 
 * This file implements the Gutenberg block for inline content generation
 * Priority: HIGH IMPACT - Makes content creation 90% easier
 * 
 * @package AI_CopyToolkit
 * @version 1.1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI CopyToolkit Gutenberg Block
 */
class AI_CopyToolkit_Gutenberg_Block {
    
    /**
     * Initialize the block
     */
    public static function init() {
        add_action('init', array(__CLASS__, 'register_block'));
        add_action('enqueue_block_editor_assets', array(__CLASS__, 'enqueue_block_assets'));
        add_action('wp_ajax_ai_copytoolkit_gutenberg_generate', array(__CLASS__, 'ajax_generate_content'));
    }
    
    /**
     * Register the Gutenberg block
     */
    public static function register_block() {
        // Register block script
        wp_register_script(
            'ai-copytoolkit-block',
            AI_COPYTOOLKIT_PLUGIN_URL . 'assets/js/gutenberg-block.js',
            array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
            AI_COPYTOOLKIT_VERSION,
            true
        );
        
        // Register block style
        wp_register_style(
            'ai-copytoolkit-block-style',
            AI_COPYTOOLKIT_PLUGIN_URL . 'assets/css/gutenberg-block.css',
            array(),
            AI_COPYTOOLKIT_VERSION
        );
        
        // Register the block
        register_block_type('ai-copytoolkit/content-generator', array(
            'editor_script' => 'ai-copytoolkit-block',
            'editor_style' => 'ai-copytoolkit-block-style',
            'render_callback' => array(__CLASS__, 'render_block'),
            'attributes' => array(
                'template' => array(
                    'type' => 'string',
                    'default' => ''
                ),
                'parameters' => array(
                    'type' => 'object',
                    'default' => array()
                ),
                'model' => array(
                    'type' => 'string',
                    'default' => 'openai/gpt-3.5-turbo'
                ),
                'generatedContent' => array(
                    'type' => 'string',
                    'default' => ''
                ),
                'isGenerating' => array(
                    'type' => 'boolean',
                    'default' => false
                )
            )
        ));
    }
    
    /**
     * Enqueue block assets
     */
    public static function enqueue_block_assets() {
        // Localize script with AJAX data
        wp_localize_script('ai-copytoolkit-block', 'aiCopyToolkitBlock', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ai_copytoolkit_nonce'),
            'templates' => self::get_templates_for_block(),
            'models' => self::get_models_for_block(),
            'strings' => array(
                'title' => __('AI Content Generator', 'ai-copytoolkit'),
                'selectTemplate' => __('Select Template', 'ai-copytoolkit'),
                'generateContent' => __('Generate Content', 'ai-copytoolkit'),
                'generating' => __('Generating...', 'ai-copytoolkit'),
                'insertContent' => __('Insert Content', 'ai-copytoolkit'),
                'regenerate' => __('Regenerate', 'ai-copytoolkit'),
                'error' => __('Error generating content', 'ai-copytoolkit')
            )
        ));
    }
    
    /**
     * Get templates formatted for block
     */
    private static function get_templates_for_block() {
        $templates = AI_CopyToolkit_Templates::instance()->get_all_templates();
        $formatted = array();
        
        foreach ($templates as $template) {
            $formatted[] = array(
                'id' => $template['id'],
                'name' => $template['name'],
                'category' => $template['category'],
                'description' => $template['description'],
                'parameters' => $template['parameters']
            );
        }
        
        return $formatted;
    }
    
    /**
     * Get models formatted for block
     */
    private static function get_models_for_block() {
        $api = AI_CopyToolkit_API::instance();
        $models = $api->fetch_available_models();
        
        if (is_wp_error($models)) {
            // Return fallback models
            return array(
                array('id' => 'openai/gpt-3.5-turbo', 'name' => 'GPT-3.5 Turbo'),
                array('id' => 'openai/gpt-4', 'name' => 'GPT-4'),
                array('id' => 'anthropic/claude-3-haiku', 'name' => 'Claude 3 Haiku')
            );
        }
        
        return array_slice($models, 0, 20); // Limit to 20 models for performance
    }
    
    /**
     * AJAX handler for content generation
     */
    public static function ajax_generate_content() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        $template_id = sanitize_text_field($_POST['template']);
        $parameters = array_map('sanitize_text_field', $_POST['parameters']);
        $model = sanitize_text_field($_POST['model']);
        
        // Get template
        $template = AI_CopyToolkit_Templates::instance()->get_template($template_id);
        if (!$template) {
            wp_send_json_error(__('Template not found', 'ai-copytoolkit'));
        }
        
        // Build prompt
        $prompt = $template['prompt'];
        foreach ($parameters as $key => $value) {
            $prompt = str_replace('{{' . $key . '}}', $value, $prompt);
        }
        
        // Generate content
        $api = AI_CopyToolkit_API::instance();
        $result = $api->generate_content($prompt, array(
            'model' => $model,
            'max_tokens' => 500,
            'temperature' => 0.7
        ));
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        // Save to history
        AI_CopyToolkit_Database::instance()->save_generation(
            get_current_user_id(),
            $template_id,
            $prompt,
            $result,
            $model
        );
        
        wp_send_json_success(array(
            'content' => $result,
            'template' => $template['name'],
            'model' => $model
        ));
    }
    
    /**
     * Render block on frontend (if needed)
     */
    public static function render_block($attributes) {
        // This block is primarily for editor use
        // Frontend rendering can be added if needed
        return '';
    }
}

// Initialize the block
AI_CopyToolkit_Gutenberg_Block::init();

/**
 * JavaScript for the Gutenberg block
 * Save this as: assets/js/gutenberg-block.js
 */
?>
<script>
(function() {
    const { registerBlockType } = wp.blocks;
    const { createElement: el, Component, Fragment } = wp.element;
    const { 
        PanelBody, 
        SelectControl, 
        TextControl, 
        Button, 
        Spinner,
        Notice
    } = wp.components;
    const { InspectorControls, RichText } = wp.blockEditor;
    const { __ } = wp.i18n;
    
    class AIContentGenerator extends Component {
        constructor(props) {
            super(props);
            this.state = {
                isGenerating: false,
                error: null,
                selectedTemplate: null
            };
        }
        
        onTemplateChange = (templateId) => {
            const template = aiCopyToolkitBlock.templates.find(t => t.id === templateId);
            this.setState({ selectedTemplate: template });
            this.props.setAttributes({ template: templateId });
        }
        
        onParameterChange = (key, value) => {
            const parameters = { ...this.props.attributes.parameters };
            parameters[key] = value;
            this.props.setAttributes({ parameters });
        }
        
        generateContent = () => {
            const { template, parameters, model } = this.props.attributes;
            
            if (!template) {
                this.setState({ error: __('Please select a template', 'ai-copytoolkit') });
                return;
            }
            
            this.setState({ isGenerating: true, error: null });
            
            const data = new FormData();
            data.append('action', 'ai_copytoolkit_gutenberg_generate');
            data.append('nonce', aiCopyToolkitBlock.nonce);
            data.append('template', template);
            data.append('model', model);
            
            Object.keys(parameters).forEach(key => {
                data.append(`parameters[${key}]`, parameters[key]);
            });
            
            fetch(aiCopyToolkitBlock.ajaxUrl, {
                method: 'POST',
                body: data
            })
            .then(response => response.json())
            .then(result => {
                this.setState({ isGenerating: false });
                
                if (result.success) {
                    this.props.setAttributes({ generatedContent: result.data.content });
                } else {
                    this.setState({ error: result.data || __('Generation failed', 'ai-copytoolkit') });
                }
            })
            .catch(error => {
                this.setState({ 
                    isGenerating: false, 
                    error: __('Network error', 'ai-copytoolkit') 
                });
            });
        }
        
        insertContent = () => {
            const { generatedContent } = this.props.attributes;
            
            // Create a new paragraph block with the generated content
            const newBlock = wp.blocks.createBlock('core/paragraph', {
                content: generatedContent
            });
            
            // Insert the block after the current block
            const { insertBlocks } = wp.data.dispatch('core/block-editor');
            const { getSelectedBlockClientId } = wp.data.select('core/block-editor');
            
            insertBlocks(newBlock, undefined, getSelectedBlockClientId());
            
            // Clear the generated content
            this.props.setAttributes({ generatedContent: '' });
        }
        
        render() {
            const { attributes } = this.props;
            const { template, parameters, model, generatedContent } = attributes;
            const { isGenerating, error, selectedTemplate } = this.state;
            
            return el(Fragment, {},
                // Inspector Controls (Sidebar)
                el(InspectorControls, {},
                    el(PanelBody, { title: __('AI Generation Settings', 'ai-copytoolkit') },
                        el(SelectControl, {
                            label: __('Template', 'ai-copytoolkit'),
                            value: template,
                            options: [
                                { label: __('Select Template', 'ai-copytoolkit'), value: '' },
                                ...aiCopyToolkitBlock.templates.map(t => ({
                                    label: t.name,
                                    value: t.id
                                }))
                            ],
                            onChange: this.onTemplateChange
                        }),
                        
                        el(SelectControl, {
                            label: __('AI Model', 'ai-copytoolkit'),
                            value: model,
                            options: aiCopyToolkitBlock.models.map(m => ({
                                label: m.name,
                                value: m.id
                            })),
                            onChange: (value) => this.props.setAttributes({ model: value })
                        })
                    )
                ),
                
                // Block Content
                el('div', { className: 'ai-copytoolkit-block' },
                    el('div', { className: 'ai-copytoolkit-header' },
                        el('h3', {}, __('🤖 AI Content Generator', 'ai-copytoolkit'))
                    ),
                    
                    // Template Parameters
                    selectedTemplate && el('div', { className: 'ai-copytoolkit-parameters' },
                        selectedTemplate.parameters.map(param => 
                            el(TextControl, {
                                key: param.name,
                                label: param.label,
                                placeholder: param.placeholder,
                                value: parameters[param.name] || '',
                                onChange: (value) => this.onParameterChange(param.name, value)
                            })
                        )
                    ),
                    
                    // Error Notice
                    error && el(Notice, {
                        status: 'error',
                        isDismissible: true,
                        onRemove: () => this.setState({ error: null })
                    }, error),
                    
                    // Generate Button
                    el('div', { className: 'ai-copytoolkit-actions' },
                        el(Button, {
                            isPrimary: true,
                            disabled: isGenerating || !template,
                            onClick: this.generateContent
                        }, 
                            isGenerating ? 
                                el(Fragment, {}, el(Spinner), ' ', __('Generating...', 'ai-copytoolkit')) :
                                __('Generate Content', 'ai-copytoolkit')
                        )
                    ),
                    
                    // Generated Content
                    generatedContent && el('div', { className: 'ai-copytoolkit-result' },
                        el('h4', {}, __('Generated Content:', 'ai-copytoolkit')),
                        el('div', { 
                            className: 'ai-copytoolkit-content',
                            dangerouslySetInnerHTML: { __html: generatedContent }
                        }),
                        el('div', { className: 'ai-copytoolkit-result-actions' },
                            el(Button, {
                                isPrimary: true,
                                onClick: this.insertContent
                            }, __('Insert Content', 'ai-copytoolkit')),
                            el(Button, {
                                isSecondary: true,
                                onClick: this.generateContent
                            }, __('Regenerate', 'ai-copytoolkit'))
                        )
                    )
                )
            );
        }
    }
    
    registerBlockType('ai-copytoolkit/content-generator', {
        title: __('AI Content Generator', 'ai-copytoolkit'),
        description: __('Generate content using AI models', 'ai-copytoolkit'),
        icon: 'robot',
        category: 'widgets',
        keywords: [
            __('ai', 'ai-copytoolkit'),
            __('content', 'ai-copytoolkit'),
            __('generator', 'ai-copytoolkit')
        ],
        supports: {
            html: false,
            reusable: false
        },
        attributes: {
            template: { type: 'string', default: '' },
            parameters: { type: 'object', default: {} },
            model: { type: 'string', default: 'openai/gpt-3.5-turbo' },
            generatedContent: { type: 'string', default: '' }
        },
        edit: AIContentGenerator,
        save: function() {
            // This block doesn't save content to post_content
            // Content is inserted as separate blocks
            return null;
        }
    });
})();
</script>

<?php
/**
 * CSS for the Gutenberg block
 * Save this as: assets/css/gutenberg-block.css
 */
?>
<style>
.ai-copytoolkit-block {
    border: 2px dashed #0073aa;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    background: #f8f9fa;
}

.ai-copytoolkit-header h3 {
    margin: 0 0 15px 0;
    color: #0073aa;
    font-size: 18px;
}

.ai-copytoolkit-parameters {
    margin-bottom: 20px;
}

.ai-copytoolkit-parameters .components-base-control {
    margin-bottom: 15px;
}

.ai-copytoolkit-actions {
    margin-bottom: 20px;
}

.ai-copytoolkit-result {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 20px;
}

.ai-copytoolkit-result h4 {
    margin: 0 0 10px 0;
    color: #333;
}

.ai-copytoolkit-content {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-family: Georgia, serif;
    line-height: 1.6;
}

.ai-copytoolkit-result-actions {
    display: flex;
    gap: 10px;
}

.ai-copytoolkit-result-actions .components-button {
    margin-right: 10px;
}

/* Loading state */
.ai-copytoolkit-block.is-generating {
    opacity: 0.7;
    pointer-events: none;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .ai-copytoolkit-result-actions {
        flex-direction: column;
    }
    
    .ai-copytoolkit-result-actions .components-button {
        margin-right: 0;
        margin-bottom: 10px;
    }
}
</style>
