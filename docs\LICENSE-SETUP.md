# AI CopyToolkit License System Setup Guide

## Quick Start (For Testing)

### Option 1: Use Demo License Keys
The plugin comes with built-in demo license keys for testing:

1. Go to **AI CopyToolkit > Settings**
2. Scroll to the "License Configuration" section
3. Use one of these demo keys:
   - `DEMO-LICENSE-KEY-2024` (Lifetime license)
   - `TEST-LICENSE-12345` (Expires 2025-12-31)

### Option 2: Bypass License Check (Development Only)
For development/testing, you can bypass the license requirement:

1. Open `ai-copytoolkit.php`
2. Find line 36 and uncomment it:
   ```php
   // Change this:
   // define('AI_COPYTOOLKIT_BYPASS_LICENSE', true);
   
   // To this:
   define('AI_COPYTOOLKIT_BYPASS_LICENSE', true);
   ```
3. Save the file

**⚠️ Important:** Remove this line before distributing your plugin!

## Setting Up Your Own License System

### Method 1: Simple Local License Keys

1. **Generate License Keys**
   - Use the license generator tool: `tools/license-generator.php`
   - Run it via command line: `php tools/license-generator.php`
   - Or access it via web browser if on a web server

2. **Add License Keys to Plugin**
   - Open `includes/class-license.php`
   - Find the `$valid_licenses` array (around line 35)
   - Add your generated license keys:

   ```php
   private $valid_licenses = array(
       'YOUR-LICENSE-KEY-HERE' => array(
           'status' => 'valid',
           'expires' => 'lifetime', // or '2025-12-31'
           'customer_name' => 'Customer Name',
           'customer_email' => '<EMAIL>',
           'activations_left' => 5
       ),
       // Add more license keys here...
   );
   ```

### Method 2: Remote License Server (Advanced)

For a professional setup, you'll want a remote license server:

1. **Set up a license server** (using services like):
   - Easy Digital Downloads (EDD) with Software Licensing extension
   - WooCommerce with license management plugins
   - Custom license server API

2. **Update the license server URL**
   - Open `includes/class-license.php`
   - Change line 30:
   ```php
   private $license_server = 'https://your-domain.com/license-api/';
   ```

3. **API Endpoints Required**
   Your license server should handle these actions:
   - `activate_license` - Activate a license key
   - `deactivate_license` - Deactivate a license key
   - `check_license` - Check license status

## License Server API Format

### Request Format
```php
POST https://your-domain.com/license-api/
{
    "action": "activate_license",
    "license": "LICENSE-KEY-HERE",
    "item_id": "ai-copytoolkit",
    "url": "https://customer-site.com",
    "version": "1.0.0"
}
```

### Response Format
```json
{
    "status": "valid",
    "expires": "2025-12-31",
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "activations_left": 4
}
```

## Popular License Server Solutions

### 1. Easy Digital Downloads (EDD)
- **Cost**: $199/year for Software Licensing extension
- **Features**: Complete license management, automatic updates
- **Setup**: Install EDD + Software Licensing extension
- **API**: Built-in license API

### 2. WooCommerce + License Manager
- **Cost**: Varies by plugin ($50-200)
- **Features**: Integrate with WooCommerce store
- **Plugins**: 
  - License Manager for WooCommerce
  - Software License Manager

### 3. Custom Solution
- **Cost**: Development time
- **Features**: Full control over license logic
- **Requirements**: PHP/MySQL knowledge

## Testing Your License System

### Test Cases to Verify

1. **Valid License Activation**
   - Enter a valid license key
   - Should show "Active" status
   - Should display customer information

2. **Invalid License Rejection**
   - Enter an invalid license key
   - Should show error message
   - Should not activate

3. **License Deactivation**
   - Deactivate an active license
   - Should clear license data
   - Should show "Inactive" status

4. **License Expiration**
   - Test with expired license
   - Should show "Expired" status
   - Should display expiration notice

### Debug License Issues

1. **Check System Logs**
   - Go to **AI CopyToolkit > System Logs**
   - Look for license-related errors

2. **Test API Connection**
   - Use browser developer tools
   - Check AJAX requests to license server
   - Verify response format

3. **Check WordPress Error Log**
   - Enable WP_DEBUG in wp-config.php
   - Check error_log file for PHP errors

## Security Considerations

### Protect License Keys
- Never store license keys in plain text logs
- Use HTTPS for all license API communication
- Validate license keys server-side only

### Prevent License Sharing
- Track activation URLs
- Limit number of activations per license
- Implement domain validation

### Secure API Communication
- Use nonce verification
- Validate all input data
- Implement rate limiting

## Customizing License Behavior

### Change License Check Frequency
Edit `includes/class-license.php` line 350:
```php
$check_interval = 24 * HOUR_IN_SECONDS; // Check daily
```

### Modify License Notices
Edit the `license_admin_notices()` method in `includes/class-license.php`

### Add Custom License Fields
Extend the license data structure in the `$valid_licenses` array

## Troubleshooting

### Common Issues

**"License server error"**
- Check license server URL
- Verify server is accessible
- Check API response format

**"Invalid response from license server"**
- Verify JSON response format
- Check for PHP errors on server
- Test API endpoint manually

**License not activating**
- Check license key format
- Verify license exists in system
- Check activation limits

### Getting Help

1. Check the system logs first
2. Test with demo license keys
3. Verify server requirements
4. Contact support with specific error messages

## Next Steps

1. **For Testing**: Use demo license keys or bypass option
2. **For Development**: Set up local license validation
3. **For Production**: Implement remote license server
4. **For Sales**: Integrate with e-commerce platform

Remember to remove any bypass options and demo keys before distributing your plugin commercially!
