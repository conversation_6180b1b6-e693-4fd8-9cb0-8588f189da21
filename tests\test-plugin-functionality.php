<?php
/**
 * Plugin Functionality Tests
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

class AI_CopyToolkit_Tests {
    
    /**
     * Run all tests
     */
    public static function run_all_tests() {
        $results = array();
        
        $results['plugin_activation'] = self::test_plugin_activation();
        $results['database_creation'] = self::test_database_creation();
        $results['settings_management'] = self::test_settings_management();
        $results['template_management'] = self::test_template_management();
        $results['security_validation'] = self::test_security_validation();
        $results['api_integration'] = self::test_api_integration();
        $results['content_generation'] = self::test_content_generation();
        $results['history_management'] = self::test_history_management();
        $results['user_permissions'] = self::test_user_permissions();
        $results['error_handling'] = self::test_error_handling();
        
        return $results;
    }
    
    /**
     * Test plugin activation
     */
    public static function test_plugin_activation() {
        $test_results = array();
        
        // Test 1: Plugin class exists
        $test_results['plugin_class_exists'] = class_exists('AI_CopyToolkit');
        
        // Test 2: Constants are defined
        $test_results['constants_defined'] = (
            defined('AI_COPYTOOLKIT_VERSION') &&
            defined('AI_COPYTOOLKIT_PLUGIN_DIR') &&
            defined('AI_COPYTOOLKIT_PLUGIN_URL') &&
            defined('AI_COPYTOOLKIT_TABLE_PREFIX')
        );
        
        // Test 3: Main instance is available
        $test_results['instance_available'] = (AI_CopyToolkit::instance() instanceof AI_CopyToolkit);
        
        // Test 4: Text domain is loaded
        $test_results['textdomain_loaded'] = function_exists('__') && __('AI CopyToolkit', 'ai-copytoolkit') !== 'AI CopyToolkit';
        
        return $test_results;
    }
    
    /**
     * Test database creation
     */
    public static function test_database_creation() {
        global $wpdb;
        $test_results = array();
        
        $tables = array(
            'templates' => $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'templates',
            'history' => $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'history',
            'api_usage' => $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'api_usage',
            'user_settings' => $wpdb->prefix . AI_COPYTOOLKIT_TABLE_PREFIX . 'user_settings'
        );
        
        foreach ($tables as $name => $table) {
            $test_results["table_{$name}_exists"] = ($wpdb->get_var("SHOW TABLES LIKE '$table'") === $table);
        }
        
        // Test default templates insertion
        $templates_count = $wpdb->get_var("SELECT COUNT(*) FROM {$tables['templates']} WHERE is_default = 1");
        $test_results['default_templates_inserted'] = ($templates_count > 0);
        
        return $test_results;
    }
    
    /**
     * Test settings management
     */
    public static function test_settings_management() {
        $test_results = array();
        
        // Test 1: Get default settings
        $settings = AI_CopyToolkit::get_settings();
        $test_results['get_settings'] = is_array($settings);
        
        // Test 2: Update settings
        $test_settings = array(
            'test_setting' => 'test_value',
            'api_key' => 'test_api_key'
        );
        $update_result = AI_CopyToolkit::update_settings($test_settings);
        $test_results['update_settings'] = $update_result;
        
        // Test 3: Verify settings were saved
        $updated_settings = AI_CopyToolkit::get_settings();
        $test_results['settings_saved'] = (
            isset($updated_settings['test_setting']) &&
            $updated_settings['test_setting'] === 'test_value'
        );
        
        // Clean up test settings
        delete_option('ai_copytoolkit_settings');
        
        return $test_results;
    }
    
    /**
     * Test template management
     */
    public static function test_template_management() {
        $test_results = array();
        
        $generator = AI_CopyToolkit_Content_Generator::instance();
        
        // Test 1: Get templates
        $templates = $generator->get_templates();
        $test_results['get_templates'] = is_array($templates) && count($templates) > 0;
        
        // Test 2: Get specific template
        if (!empty($templates)) {
            $template = $generator->get_template($templates[0]->id);
            $test_results['get_single_template'] = ($template !== null);
        } else {
            $test_results['get_single_template'] = false;
        }
        
        // Test 3: Create custom template
        $template_data = array(
            'name' => 'Test Template',
            'description' => 'Test template description',
            'category' => 'test',
            'content_type' => 'test_content',
            'prompt_template' => 'Test prompt for {test_param}',
            'parameters' => array(
                'test_param' => array(
                    'type' => 'text',
                    'required' => true,
                    'label' => 'Test Parameter'
                )
            )
        );
        
        $template_id = $generator->save_template($template_data);
        $test_results['create_template'] = !is_wp_error($template_id) && $template_id > 0;
        
        // Test 4: Delete template
        if ($test_results['create_template']) {
            $delete_result = $generator->delete_template($template_id);
            $test_results['delete_template'] = !is_wp_error($delete_result);
        } else {
            $test_results['delete_template'] = false;
        }
        
        return $test_results;
    }
    
    /**
     * Test security validation
     */
    public static function test_security_validation() {
        $test_results = array();
        
        // Test 1: Input sanitization
        $dirty_input = '<script>alert("xss")</script>Test';
        $clean_input = AI_CopyToolkit_Security::sanitize_input($dirty_input, 'text');
        $test_results['input_sanitization'] = ($clean_input !== $dirty_input && strpos($clean_input, '<script>') === false);
        
        // Test 2: Email validation
        $valid_email = AI_CopyToolkit_Security::validate_input('<EMAIL>', 'email');
        $invalid_email = AI_CopyToolkit_Security::validate_input('invalid-email', 'email');
        $test_results['email_validation'] = ($valid_email === true && is_wp_error($invalid_email));
        
        // Test 3: Number validation
        $valid_number = AI_CopyToolkit_Security::validate_input('123', 'int', array('min' => 1, 'max' => 1000));
        $invalid_number = AI_CopyToolkit_Security::validate_input('abc', 'int');
        $test_results['number_validation'] = ($valid_number === true && is_wp_error($invalid_number));
        
        // Test 4: Required field validation
        $required_empty = AI_CopyToolkit_Security::validate_input('', 'required');
        $required_filled = AI_CopyToolkit_Security::validate_input('value', 'required');
        $test_results['required_validation'] = (is_wp_error($required_empty) && $required_filled === true);
        
        return $test_results;
    }
    
    /**
     * Test API integration
     */
    public static function test_api_integration() {
        $test_results = array();
        
        $api = AI_CopyToolkit_API::instance();
        
        // Test 1: API class instantiation
        $test_results['api_instance'] = ($api instanceof AI_CopyToolkit_API);
        
        // Test 2: Model fetching (without API key)
        $models = $api->fetch_available_models();
        $test_results['models_fetch_no_key'] = is_wp_error($models);
        
        // Test 3: Content generation (without API key)
        $generation = $api->generate_content('Test prompt');
        $test_results['generation_no_key'] = is_wp_error($generation);
        
        // Note: Actual API tests would require a valid API key
        // These tests verify error handling when no key is present
        
        return $test_results;
    }
    
    /**
     * Test content generation
     */
    public static function test_content_generation() {
        $test_results = array();
        
        $generator = AI_CopyToolkit_Content_Generator::instance();
        
        // Test 1: Generator instance
        $test_results['generator_instance'] = ($generator instanceof AI_CopyToolkit_Content_Generator);
        
        // Test 2: Prompt building
        $template = (object) array(
            'prompt_template' => 'Write about {topic} for {audience}',
            'parameters' => json_encode(array(
                'topic' => array('type' => 'text'),
                'audience' => array('type' => 'text')
            ))
        );
        
        $parameters = array(
            'topic' => 'AI technology',
            'audience' => 'developers'
        );
        
        // Use reflection to test private method
        $reflection = new ReflectionClass($generator);
        $method = $reflection->getMethod('build_prompt_from_template');
        $method->setAccessible(true);
        $prompt = $method->invoke($generator, $template, $parameters);
        
        $test_results['prompt_building'] = (
            strpos($prompt, 'AI technology') !== false &&
            strpos($prompt, 'developers') !== false
        );
        
        return $test_results;
    }
    
    /**
     * Test history management
     */
    public static function test_history_management() {
        global $wpdb;
        $test_results = array();
        
        $table_name = AI_CopyToolkit_Database::get_table_name('history');
        
        // Test 1: History table exists
        $test_results['history_table_exists'] = ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name);
        
        // Test 2: Insert test history entry
        $test_data = array(
            'user_id' => 1,
            'model_used' => 'test-model',
            'prompt_text' => 'Test prompt',
            'generated_content' => 'Test content',
            'tokens_used' => 100,
            'generation_time' => 1.5,
            'status' => 'completed'
        );
        
        $insert_result = $wpdb->insert($table_name, $test_data);
        $test_results['history_insert'] = ($insert_result !== false);
        
        // Test 3: Retrieve history entry
        if ($test_results['history_insert']) {
            $history_id = $wpdb->insert_id;
            $retrieved = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $history_id));
            $test_results['history_retrieve'] = ($retrieved !== null);
            
            // Clean up
            $wpdb->delete($table_name, array('id' => $history_id));
        } else {
            $test_results['history_retrieve'] = false;
        }
        
        return $test_results;
    }
    
    /**
     * Test user permissions
     */
    public static function test_user_permissions() {
        $test_results = array();
        
        // Test 1: Capability checking function exists
        $test_results['capability_function_exists'] = method_exists('AI_CopyToolkit_Security', 'check_capability');
        
        // Test 2: Admin capability check
        $admin_check = AI_CopyToolkit_Security::check_capability('manage_options', 1);
        $test_results['admin_capability'] = is_bool($admin_check);
        
        // Test 3: Editor capability check
        $editor_check = AI_CopyToolkit_Security::check_capability('edit_posts', 1);
        $test_results['editor_capability'] = is_bool($editor_check);
        
        return $test_results;
    }
    
    /**
     * Test error handling
     */
    public static function test_error_handling() {
        $test_results = array();
        
        $logger = AI_CopyToolkit_Logger::instance();
        
        // Test 1: Logger instance
        $test_results['logger_instance'] = ($logger instanceof AI_CopyToolkit_Logger);
        
        // Test 2: Error logging
        $logger->error('Test error message', array('test' => 'context'));
        $test_results['error_logging'] = true; // If no exception thrown, logging works
        
        // Test 3: Log retrieval
        $logs = $logger->get_recent_logs(10);
        $test_results['log_retrieval'] = is_array($logs);
        
        return $test_results;
    }
    
    /**
     * Generate test report
     */
    public static function generate_report($results) {
        $total_tests = 0;
        $passed_tests = 0;
        
        $report = "AI CopyToolkit Test Report\n";
        $report .= "==========================\n\n";
        
        foreach ($results as $category => $tests) {
            $report .= ucwords(str_replace('_', ' ', $category)) . ":\n";
            
            foreach ($tests as $test_name => $result) {
                $total_tests++;
                $status = $result ? 'PASS' : 'FAIL';
                if ($result) $passed_tests++;
                
                $report .= "  - " . ucwords(str_replace('_', ' ', $test_name)) . ": $status\n";
            }
            
            $report .= "\n";
        }
        
        $success_rate = round(($passed_tests / $total_tests) * 100, 2);
        $report .= "Summary:\n";
        $report .= "--------\n";
        $report .= "Total Tests: $total_tests\n";
        $report .= "Passed: $passed_tests\n";
        $report .= "Failed: " . ($total_tests - $passed_tests) . "\n";
        $report .= "Success Rate: $success_rate%\n";
        
        return $report;
    }
}
