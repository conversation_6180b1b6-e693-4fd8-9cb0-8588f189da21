<?php
/**
 * System logs page
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check permissions
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.', 'ai-copytoolkit'));
}

$logger = AI_CopyToolkit_Logger::instance();
$log_stats = $logger->get_log_stats();
?>

<div class="wrap ai-copytoolkit-logs">
    <h1 class="wp-heading-inline">
        <?php _e('System Logs', 'ai-copytoolkit'); ?>
    </h1>
    
    <div class="ai-copytoolkit-logs-container">
        <!-- Log Statistics -->
        <div class="ai-copytoolkit-card log-stats">
            <h2><?php _e('Log Statistics', 'ai-copytoolkit'); ?></h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format($log_stats['total_entries']); ?></div>
                    <div class="stat-label"><?php _e('Total Entries', 'ai-copytoolkit'); ?></div>
                </div>
                <div class="stat-item error">
                    <div class="stat-number"><?php echo number_format($log_stats['error_count']); ?></div>
                    <div class="stat-label"><?php _e('Errors', 'ai-copytoolkit'); ?></div>
                </div>
                <div class="stat-item warning">
                    <div class="stat-number"><?php echo number_format($log_stats['warning_count']); ?></div>
                    <div class="stat-label"><?php _e('Warnings', 'ai-copytoolkit'); ?></div>
                </div>
                <div class="stat-item info">
                    <div class="stat-number"><?php echo number_format($log_stats['info_count']); ?></div>
                    <div class="stat-label"><?php _e('Info', 'ai-copytoolkit'); ?></div>
                </div>
            </div>
            <div class="log-file-info">
                <p>
                    <strong><?php _e('Log File Size:', 'ai-copytoolkit'); ?></strong>
                    <?php echo size_format($log_stats['file_size']); ?>
                </p>
            </div>
        </div>
        
        <!-- Log Controls -->
        <div class="ai-copytoolkit-card log-controls">
            <h2><?php _e('Log Management', 'ai-copytoolkit'); ?></h2>
            <div class="control-buttons">
                <button id="refresh-logs" class="button button-secondary">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('Refresh Logs', 'ai-copytoolkit'); ?>
                </button>
                <button id="download-logs" class="button button-secondary">
                    <span class="dashicons dashicons-download"></span>
                    <?php _e('Download Logs', 'ai-copytoolkit'); ?>
                </button>
                <button id="clear-logs" class="button button-secondary button-danger">
                    <span class="dashicons dashicons-trash"></span>
                    <?php _e('Clear All Logs', 'ai-copytoolkit'); ?>
                </button>
            </div>
        </div>
        
        <!-- Log Filters -->
        <div class="ai-copytoolkit-card log-filters">
            <h2><?php _e('Filter Logs', 'ai-copytoolkit'); ?></h2>
            <div class="filter-controls">
                <div class="filter-group">
                    <label for="log-level-filter"><?php _e('Log Level:', 'ai-copytoolkit'); ?></label>
                    <select id="log-level-filter">
                        <option value=""><?php _e('All Levels', 'ai-copytoolkit'); ?></option>
                        <option value="EMERGENCY"><?php _e('Emergency', 'ai-copytoolkit'); ?></option>
                        <option value="ALERT"><?php _e('Alert', 'ai-copytoolkit'); ?></option>
                        <option value="CRITICAL"><?php _e('Critical', 'ai-copytoolkit'); ?></option>
                        <option value="ERROR"><?php _e('Error', 'ai-copytoolkit'); ?></option>
                        <option value="WARNING"><?php _e('Warning', 'ai-copytoolkit'); ?></option>
                        <option value="NOTICE"><?php _e('Notice', 'ai-copytoolkit'); ?></option>
                        <option value="INFO"><?php _e('Info', 'ai-copytoolkit'); ?></option>
                        <option value="DEBUG"><?php _e('Debug', 'ai-copytoolkit'); ?></option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="log-limit"><?php _e('Number of Entries:', 'ai-copytoolkit'); ?></label>
                    <select id="log-limit">
                        <option value="50">50</option>
                        <option value="100" selected>100</option>
                        <option value="200">200</option>
                        <option value="500">500</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <button id="apply-filters" class="button button-primary">
                        <?php _e('Apply Filters', 'ai-copytoolkit'); ?>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Log Entries -->
        <div class="ai-copytoolkit-card log-entries">
            <h2><?php _e('Recent Log Entries', 'ai-copytoolkit'); ?></h2>
            <div id="log-entries-container">
                <div class="loading-spinner">
                    <span class="spinner is-active"></span>
                    <?php _e('Loading log entries...', 'ai-copytoolkit'); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Load initial logs
    loadLogEntries();
    
    // Refresh logs
    $('#refresh-logs').on('click', function() {
        loadLogEntries();
    });
    
    // Apply filters
    $('#apply-filters').on('click', function() {
        loadLogEntries();
    });
    
    // Download logs
    $('#download-logs').on('click', function() {
        window.location.href = ajaxurl + '?action=ai_copytoolkit_download_logs&nonce=' + aiCopyToolkit.nonce;
    });
    
    // Clear logs
    $('#clear-logs').on('click', function() {
        if (confirm('<?php _e('Are you sure you want to clear all log files? This action cannot be undone.', 'ai-copytoolkit'); ?>')) {
            clearLogs();
        }
    });
    
    function loadLogEntries() {
        const $container = $('#log-entries-container');
        $container.html('<div class="loading-spinner"><span class="spinner is-active"></span> <?php _e('Loading log entries...', 'ai-copytoolkit'); ?></div>');
        
        const level = $('#log-level-filter').val();
        const limit = parseInt($('#log-limit').val());
        
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_get_logs',
            nonce: aiCopyToolkit.nonce,
            level: level,
            limit: limit
        }, function(response) {
            if (response.success) {
                displayLogEntries(response.data);
            } else {
                $container.html('<div class="error-message"><?php _e('Failed to load log entries.', 'ai-copytoolkit'); ?></div>');
            }
        }).fail(function() {
            $container.html('<div class="error-message"><?php _e('Failed to load log entries.', 'ai-copytoolkit'); ?></div>');
        });
    }
    
    function displayLogEntries(logs) {
        const $container = $('#log-entries-container');
        
        if (logs.length === 0) {
            $container.html('<div class="no-logs"><?php _e('No log entries found.', 'ai-copytoolkit'); ?></div>');
            return;
        }
        
        let html = '<div class="log-entries-list">';
        
        logs.forEach(function(log) {
            const levelClass = log.level.toLowerCase();
            const timestamp = new Date(log.timestamp).toLocaleString();
            
            html += '<div class="log-entry log-' + levelClass + '">';
            html += '<div class="log-header">';
            html += '<span class="log-level">' + log.level + '</span>';
            html += '<span class="log-timestamp">' + timestamp + '</span>';
            html += '</div>';
            html += '<div class="log-message">' + escapeHtml(log.message) + '</div>';
            
            if (log.context && Object.keys(log.context).length > 0) {
                html += '<div class="log-context">';
                html += '<strong><?php _e('Context:', 'ai-copytoolkit'); ?></strong>';
                html += '<pre>' + JSON.stringify(log.context, null, 2) + '</pre>';
                html += '</div>';
            }
            
            html += '<div class="log-meta">';
            if (log.user_id) {
                html += '<span class="log-user">User ID: ' + log.user_id + '</span>';
            }
            if (log.ip_address) {
                html += '<span class="log-ip">IP: ' + log.ip_address + '</span>';
            }
            html += '</div>';
            html += '</div>';
        });
        
        html += '</div>';
        $container.html(html);
    }
    
    function clearLogs() {
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_clear_logs',
            nonce: aiCopyToolkit.nonce
        }, function(response) {
            if (response.success) {
                showNotice(response.data, 'success');
                loadLogEntries();
                location.reload(); // Refresh to update stats
            } else {
                showNotice(response.data || '<?php _e('Failed to clear logs', 'ai-copytoolkit'); ?>', 'error');
            }
        });
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function showNotice(message, type) {
        const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        $('.ai-copytoolkit-logs .wp-heading-inline').after($notice);
        
        setTimeout(function() {
            $notice.fadeOut();
        }, 3000);
    }
});
</script>
