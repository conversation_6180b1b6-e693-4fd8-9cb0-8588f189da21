<?php
/**
 * Security and validation helper class
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI_CopyToolkit_Security class
 */
class AI_CopyToolkit_Security {
    
    /**
     * Instance
     *
     * @var AI_CopyToolkit_Security
     */
    private static $instance = null;
    
    /**
     * Get instance
     *
     * @return AI_CopyToolkit_Security
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'init_security'));
    }
    
    /**
     * Initialize security measures
     */
    public function init_security() {
        // Add security headers
        add_action('send_headers', array($this, 'add_security_headers'));
        
        // Sanitize all inputs
        add_action('wp_loaded', array($this, 'sanitize_global_inputs'));
        
        // Rate limiting
        add_action('wp_ajax_ai_copytoolkit_generate_content', array($this, 'check_rate_limit'), 1);
        add_action('wp_ajax_nopriv_ai_copytoolkit_generate_content', array($this, 'check_rate_limit'), 1);
    }
    
    /**
     * Add security headers
     */
    public function add_security_headers() {
        if (is_admin() && strpos($_SERVER['REQUEST_URI'], 'ai-copytoolkit') !== false) {
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');
            header('X-XSS-Protection: 1; mode=block');
            header('Referrer-Policy: strict-origin-when-cross-origin');
        }
    }
    
    /**
     * Sanitize global inputs
     */
    public function sanitize_global_inputs() {
        if (is_admin() && isset($_REQUEST['page']) && strpos($_REQUEST['page'], 'ai-copytoolkit') === 0) {
            $_GET = $this->sanitize_array($_GET);
            $_POST = $this->sanitize_array($_POST);
            $_REQUEST = $this->sanitize_array($_REQUEST);
        }
    }
    
    /**
     * Check rate limit for API requests
     */
    public function check_rate_limit() {
        $user_id = get_current_user_id();
        $ip_address = $this->get_client_ip();
        
        // Check user-based rate limit
        if ($user_id > 0) {
            $user_limit = $this->check_user_rate_limit($user_id);
            if (!$user_limit) {
                wp_die(__('Rate limit exceeded. Please try again later.', 'ai-copytoolkit'), 429);
            }
        }
        
        // Check IP-based rate limit
        $ip_limit = $this->check_ip_rate_limit($ip_address);
        if (!$ip_limit) {
            wp_die(__('Rate limit exceeded. Please try again later.', 'ai-copytoolkit'), 429);
        }
    }
    
    /**
     * Verify nonce for AJAX requests
     *
     * @param string $action
     * @param string $nonce_field
     * @return bool
     */
    public static function verify_nonce($action = 'ai_copytoolkit_nonce', $nonce_field = 'nonce') {
        if (!isset($_REQUEST[$nonce_field])) {
            return false;
        }
        
        return wp_verify_nonce($_REQUEST[$nonce_field], $action);
    }
    
    /**
     * Check user capabilities
     *
     * @param string $capability
     * @param int $user_id
     * @return bool
     */
    public static function check_capability($capability = 'edit_posts', $user_id = null) {
        if (null === $user_id) {
            $user_id = get_current_user_id();
        }
        
        return user_can($user_id, $capability);
    }
    
    /**
     * Sanitize input data
     *
     * @param mixed $data
     * @param string $type
     * @return mixed
     */
    public static function sanitize_input($data, $type = 'text') {
        switch ($type) {
            case 'email':
                return sanitize_email($data);
                
            case 'url':
                return esc_url_raw($data);
                
            case 'int':
                return intval($data);
                
            case 'float':
                return floatval($data);
                
            case 'bool':
                return (bool) $data;
                
            case 'textarea':
                return sanitize_textarea_field($data);
                
            case 'html':
                return wp_kses_post($data);
                
            case 'key':
                return sanitize_key($data);
                
            case 'slug':
                return sanitize_title($data);
                
            case 'array':
                return is_array($data) ? array_map(array(__CLASS__, 'sanitize_input'), $data) : array();
                
            case 'json':
                if (is_string($data)) {
                    $decoded = json_decode($data, true);
                    return is_array($decoded) ? $decoded : array();
                }
                return is_array($data) ? $data : array();
                
            case 'text':
            default:
                return sanitize_text_field($data);
        }
    }
    
    /**
     * Validate input data
     *
     * @param mixed $data
     * @param string $type
     * @param array $options
     * @return bool|WP_Error
     */
    public static function validate_input($data, $type = 'text', $options = array()) {
        switch ($type) {
            case 'email':
                if (!is_email($data)) {
                    return new WP_Error('invalid_email', __('Invalid email address', 'ai-copytoolkit'));
                }
                break;
                
            case 'url':
                if (!filter_var($data, FILTER_VALIDATE_URL)) {
                    return new WP_Error('invalid_url', __('Invalid URL', 'ai-copytoolkit'));
                }
                break;
                
            case 'int':
                if (!is_numeric($data)) {
                    return new WP_Error('invalid_int', __('Invalid number', 'ai-copytoolkit'));
                }
                
                $value = intval($data);
                if (isset($options['min']) && $value < $options['min']) {
                    return new WP_Error('value_too_small', sprintf(__('Value must be at least %d', 'ai-copytoolkit'), $options['min']));
                }
                
                if (isset($options['max']) && $value > $options['max']) {
                    return new WP_Error('value_too_large', sprintf(__('Value must be no more than %d', 'ai-copytoolkit'), $options['max']));
                }
                break;
                
            case 'float':
                if (!is_numeric($data)) {
                    return new WP_Error('invalid_float', __('Invalid decimal number', 'ai-copytoolkit'));
                }
                
                $value = floatval($data);
                if (isset($options['min']) && $value < $options['min']) {
                    return new WP_Error('value_too_small', sprintf(__('Value must be at least %f', 'ai-copytoolkit'), $options['min']));
                }
                
                if (isset($options['max']) && $value > $options['max']) {
                    return new WP_Error('value_too_large', sprintf(__('Value must be no more than %f', 'ai-copytoolkit'), $options['max']));
                }
                break;
                
            case 'string':
                if (!is_string($data)) {
                    return new WP_Error('invalid_string', __('Invalid string', 'ai-copytoolkit'));
                }
                
                $length = strlen($data);
                if (isset($options['min_length']) && $length < $options['min_length']) {
                    return new WP_Error('string_too_short', sprintf(__('String must be at least %d characters', 'ai-copytoolkit'), $options['min_length']));
                }
                
                if (isset($options['max_length']) && $length > $options['max_length']) {
                    return new WP_Error('string_too_long', sprintf(__('String must be no more than %d characters', 'ai-copytoolkit'), $options['max_length']));
                }
                
                if (isset($options['pattern']) && !preg_match($options['pattern'], $data)) {
                    return new WP_Error('invalid_pattern', __('String format is invalid', 'ai-copytoolkit'));
                }
                break;
                
            case 'required':
                if (empty($data)) {
                    return new WP_Error('required_field', __('This field is required', 'ai-copytoolkit'));
                }
                break;
                
            case 'api_key':
                if (empty($data)) {
                    return new WP_Error('empty_api_key', __('API key is required', 'ai-copytoolkit'));
                }
                
                if (strlen($data) < 10) {
                    return new WP_Error('invalid_api_key', __('API key appears to be invalid', 'ai-copytoolkit'));
                }
                break;
        }
        
        return true;
    }
    
    /**
     * Escape output data
     *
     * @param mixed $data
     * @param string $context
     * @return mixed
     */
    public static function escape_output($data, $context = 'html') {
        switch ($context) {
            case 'attr':
                return esc_attr($data);
                
            case 'url':
                return esc_url($data);
                
            case 'js':
                return esc_js($data);
                
            case 'textarea':
                return esc_textarea($data);
                
            case 'html':
                return wp_kses_post($data);
                
            case 'json':
                return wp_json_encode($data);
                
            default:
                return esc_html($data);
        }
    }
    
    /**
     * Sanitize array recursively
     *
     * @param array $array
     * @return array
     */
    private function sanitize_array($array) {
        if (!is_array($array)) {
            return sanitize_text_field($array);
        }
        
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = $this->sanitize_array($value);
            } else {
                $array[$key] = sanitize_text_field($value);
            }
        }
        
        return $array;
    }
    
    /**
     * Check user rate limit
     *
     * @param int $user_id
     * @return bool
     */
    private function check_user_rate_limit($user_id) {
        $transient_key = 'ai_copytoolkit_user_rate_' . $user_id;
        $requests = get_transient($transient_key);
        
        if (false === $requests) {
            $requests = 0;
        }
        
        // Allow 60 requests per hour per user
        if ($requests >= 60) {
            return false;
        }
        
        set_transient($transient_key, $requests + 1, HOUR_IN_SECONDS);
        return true;
    }
    
    /**
     * Check IP rate limit
     *
     * @param string $ip_address
     * @return bool
     */
    private function check_ip_rate_limit($ip_address) {
        $transient_key = 'ai_copytoolkit_ip_rate_' . md5($ip_address);
        $requests = get_transient($transient_key);
        
        if (false === $requests) {
            $requests = 0;
        }
        
        // Allow 100 requests per hour per IP
        if ($requests >= 100) {
            return false;
        }
        
        set_transient($transient_key, $requests + 1, HOUR_IN_SECONDS);
        return true;
    }
    
    /**
     * Get client IP address
     *
     * @return string
     */
    private function get_client_ip() {
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                
                $ip = trim($ip);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * Log security event
     *
     * @param string $event
     * @param array $data
     */
    public static function log_security_event($event, $data = array()) {
        $settings = AI_CopyToolkit::get_settings();
        
        if (!isset($settings['enable_logging']) || !$settings['enable_logging']) {
            return;
        }
        
        $log_data = array(
            'timestamp' => current_time('mysql'),
            'event' => $event,
            'user_id' => get_current_user_id(),
            'ip_address' => self::get_client_ip(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'data' => $data
        );
        
        error_log('AI CopyToolkit Security: ' . wp_json_encode($log_data));
    }
    
    /**
     * Check if request is from admin area
     *
     * @return bool
     */
    public static function is_admin_request() {
        return is_admin() && !wp_doing_ajax() && !wp_doing_cron();
    }
    
    /**
     * Check if request is AJAX
     *
     * @return bool
     */
    public static function is_ajax_request() {
        return wp_doing_ajax();
    }
    
    /**
     * Generate secure random string
     *
     * @param int $length
     * @return string
     */
    public static function generate_random_string($length = 32) {
        if (function_exists('random_bytes')) {
            return bin2hex(random_bytes($length / 2));
        } elseif (function_exists('openssl_random_pseudo_bytes')) {
            return bin2hex(openssl_random_pseudo_bytes($length / 2));
        } else {
            return wp_generate_password($length, false);
        }
    }
}
