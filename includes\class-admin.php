<?php
/**
 * Admin interface class
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI_CopyToolkit_Admin class
 */
class AI_CopyToolkit_Admin {
    
    /**
     * Instance
     *
     * @var AI_CopyToolkit_Admin
     */
    private static $instance = null;
    
    /**
     * Get instance
     *
     * @return AI_CopyToolkit_Admin
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('wp_ajax_ai_copytoolkit_save_settings', array($this, 'save_settings'));
        add_action('wp_ajax_ai_copytoolkit_get_history', array($this, 'get_history'));
        add_action('wp_ajax_ai_copytoolkit_delete_history', array($this, 'delete_history'));
        add_action('wp_ajax_ai_copytoolkit_export_history', array($this, 'export_history'));
        add_action('wp_ajax_ai_copytoolkit_clear_all_history', array($this, 'clear_all_history'));
        add_action('wp_ajax_ai_copytoolkit_get_history_item', array($this, 'get_history_item'));
        add_action('wp_ajax_ai_copytoolkit_get_logs', array($this, 'get_logs'));
        add_action('wp_ajax_ai_copytoolkit_save_default_settings', array($this, 'save_default_settings'));
        
        // Add admin notices
        add_action('admin_notices', array($this, 'admin_notices'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        $capability = 'edit_posts';
        
        // Main menu page
        add_menu_page(
            __('AI CopyToolkit', 'ai-copytoolkit'),
            __('AI CopyToolkit', 'ai-copytoolkit'),
            $capability,
            'ai-copytoolkit',
            array($this, 'dashboard_page'),
            'dashicons-edit-large',
            30
        );
        
        // Dashboard submenu
        add_submenu_page(
            'ai-copytoolkit',
            __('Dashboard', 'ai-copytoolkit'),
            __('Dashboard', 'ai-copytoolkit'),
            $capability,
            'ai-copytoolkit',
            array($this, 'dashboard_page')
        );
        
        // Content Generator submenu
        add_submenu_page(
            'ai-copytoolkit',
            __('Generate Content', 'ai-copytoolkit'),
            __('Generate Content', 'ai-copytoolkit'),
            $capability,
            'ai-copytoolkit-generate',
            array($this, 'generate_page')
        );
        
        // Templates submenu
        add_submenu_page(
            'ai-copytoolkit',
            __('Templates', 'ai-copytoolkit'),
            __('Templates', 'ai-copytoolkit'),
            $capability,
            'ai-copytoolkit-templates',
            array($this, 'templates_page')
        );
        
        // History submenu
        add_submenu_page(
            'ai-copytoolkit',
            __('History', 'ai-copytoolkit'),
            __('History', 'ai-copytoolkit'),
            $capability,
            'ai-copytoolkit-history',
            array($this, 'history_page')
        );
        
        // Settings submenu
        add_submenu_page(
            'ai-copytoolkit',
            __('Settings', 'ai-copytoolkit'),
            __('Settings', 'ai-copytoolkit'),
            'manage_options',
            'ai-copytoolkit-settings',
            array($this, 'settings_page')
        );

        // Logs submenu (only for administrators)
        add_submenu_page(
            'ai-copytoolkit',
            __('System Logs', 'ai-copytoolkit'),
            __('System Logs', 'ai-copytoolkit'),
            'manage_options',
            'ai-copytoolkit-logs',
            array($this, 'logs_page')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'ai-copytoolkit') === false) {
            return;
        }
        
        // Enqueue styles
        wp_enqueue_style(
            'ai-copytoolkit-admin',
            AI_COPYTOOLKIT_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            AI_COPYTOOLKIT_VERSION
        );
        
        // Enqueue scripts
        wp_enqueue_script(
            'ai-copytoolkit-admin',
            AI_COPYTOOLKIT_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'wp-util'),
            AI_COPYTOOLKIT_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('ai-copytoolkit-admin', 'aiCopyToolkit', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ai_copytoolkit_nonce'),
            'strings' => array(
                'generating' => __('Generating content...', 'ai-copytoolkit'),
                'error' => __('An error occurred. Please try again.', 'ai-copytoolkit'),
                'success' => __('Content generated successfully!', 'ai-copytoolkit'),
                'confirm_delete' => __('Are you sure you want to delete this item?', 'ai-copytoolkit'),
                'copied' => __('Copied to clipboard!', 'ai-copytoolkit'),
                'copy_failed' => __('Failed to copy to clipboard', 'ai-copytoolkit'),
            )
        ));
        
        // Enqueue WordPress media scripts for file uploads
        wp_enqueue_media();
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('ai_copytoolkit_settings', 'ai_copytoolkit_settings', array(
            'sanitize_callback' => array($this, 'sanitize_settings')
        ));
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($settings) {
        $sanitized = array();
        
        if (isset($settings['api_key'])) {
            $sanitized['api_key'] = sanitize_text_field($settings['api_key']);
        }
        
        if (isset($settings['default_model'])) {
            $sanitized['default_model'] = sanitize_text_field($settings['default_model']);
        }
        
        if (isset($settings['max_tokens'])) {
            $sanitized['max_tokens'] = intval($settings['max_tokens']);
        }
        
        if (isset($settings['temperature'])) {
            $sanitized['temperature'] = floatval($settings['temperature']);
        }
        
        if (isset($settings['enable_history'])) {
            $sanitized['enable_history'] = (bool) $settings['enable_history'];
        }
        
        if (isset($settings['history_limit'])) {
            $sanitized['history_limit'] = intval($settings['history_limit']);
        }
        
        if (isset($settings['enable_logging'])) {
            $sanitized['enable_logging'] = (bool) $settings['enable_logging'];
        }
        
        return $sanitized;
    }
    
    /**
     * Dashboard page
     */
    public function dashboard_page() {
        $this->render_page('dashboard');
    }
    
    /**
     * Generate content page
     */
    public function generate_page() {
        $this->render_page('generate');
    }
    
    /**
     * Templates page
     */
    public function templates_page() {
        $this->render_page('templates');
    }
    
    /**
     * History page
     */
    public function history_page() {
        $this->render_page('history');
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        $this->render_page('settings');
    }

    /**
     * Logs page
     */
    public function logs_page() {
        $this->render_page('logs');
    }
    
    /**
     * Render admin page
     */
    private function render_page($page) {
        $template_file = AI_COPYTOOLKIT_PLUGIN_DIR . 'admin/pages/' . $page . '.php';
        
        if (file_exists($template_file)) {
            include $template_file;
        } else {
            echo '<div class="wrap"><h1>' . __('Page not found', 'ai-copytoolkit') . '</h1></div>';
        }
    }
    
    /**
     * Save settings via AJAX
     */
    public function save_settings() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        $settings = $_POST['settings'];
        $sanitized_settings = $this->sanitize_settings($settings);
        
        $result = AI_CopyToolkit::update_settings($sanitized_settings);
        
        if ($result) {
            wp_send_json_success(__('Settings saved successfully', 'ai-copytoolkit'));
        } else {
            wp_send_json_error(__('Failed to save settings', 'ai-copytoolkit'));
        }
    }
    
    /**
     * Get generation history via AJAX
     */
    public function get_history() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        global $wpdb;
        
        $user_id = get_current_user_id();
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $per_page = isset($_POST['per_page']) ? intval($_POST['per_page']) : 20;
        $offset = ($page - 1) * $per_page;
        
        $table_name = AI_CopyToolkit_Database::get_table_name('history');
        $templates_table = AI_CopyToolkit_Database::get_table_name('templates');
        
        // Get total count
        $total = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
            $user_id
        ));
        
        // Get history entries
        $history = $wpdb->get_results($wpdb->prepare("
            SELECT h.*, t.name as template_name 
            FROM $table_name h
            LEFT JOIN $templates_table t ON h.template_id = t.id
            WHERE h.user_id = %d
            ORDER BY h.created_at DESC
            LIMIT %d OFFSET %d
        ", $user_id, $per_page, $offset));
        
        wp_send_json_success(array(
            'history' => $history,
            'total' => $total,
            'page' => $page,
            'per_page' => $per_page,
            'total_pages' => ceil($total / $per_page)
        ));
    }
    
    /**
     * Delete history entry via AJAX
     */
    public function delete_history() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        global $wpdb;
        
        $history_id = intval($_POST['history_id']);
        $user_id = get_current_user_id();
        $table_name = AI_CopyToolkit_Database::get_table_name('history');
        
        $result = $wpdb->delete(
            $table_name,
            array(
                'id' => $history_id,
                'user_id' => $user_id
            ),
            array('%d', '%d')
        );
        
        if ($result) {
            wp_send_json_success(__('History entry deleted', 'ai-copytoolkit'));
        } else {
            wp_send_json_error(__('Failed to delete history entry', 'ai-copytoolkit'));
        }
    }
    
    /**
     * Export history via AJAX
     */
    public function export_history() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        global $wpdb;
        
        $user_id = get_current_user_id();
        $format = isset($_POST['format']) ? sanitize_text_field($_POST['format']) : 'csv';
        $table_name = AI_CopyToolkit_Database::get_table_name('history');
        $templates_table = AI_CopyToolkit_Database::get_table_name('templates');
        
        $history = $wpdb->get_results($wpdb->prepare("
            SELECT h.*, t.name as template_name 
            FROM $table_name h
            LEFT JOIN $templates_table t ON h.template_id = t.id
            WHERE h.user_id = %d
            ORDER BY h.created_at DESC
        ", $user_id));
        
        if (empty($history)) {
            wp_send_json_error(__('No history to export', 'ai-copytoolkit'));
        }
        
        $export_data = $this->prepare_export_data($history, $format);
        
        wp_send_json_success(array(
            'data' => $export_data,
            'filename' => 'ai-copytoolkit-history-' . date('Y-m-d') . '.' . $format
        ));
    }
    
    /**
     * Prepare export data
     */
    private function prepare_export_data($history, $format) {
        if ($format === 'json') {
            return wp_json_encode($history, JSON_PRETTY_PRINT);
        }
        
        // CSV format
        $csv_data = array();
        $csv_data[] = array(
            'Date',
            'Template',
            'Model',
            'Prompt',
            'Generated Content',
            'Tokens Used',
            'Generation Time',
            'Status'
        );
        
        foreach ($history as $entry) {
            $csv_data[] = array(
                $entry->created_at,
                $entry->template_name ?: 'Custom',
                $entry->model_used,
                substr($entry->prompt_text, 0, 100) . '...',
                substr($entry->generated_content, 0, 200) . '...',
                $entry->tokens_used,
                $entry->generation_time,
                $entry->status
            );
        }
        
        $output = '';
        foreach ($csv_data as $row) {
            $output .= '"' . implode('","', $row) . '"' . "\n";
        }
        
        return $output;
    }

    /**
     * Clear all history via AJAX
     */
    public function clear_all_history() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }

        global $wpdb;

        $user_id = get_current_user_id();
        $table_name = AI_CopyToolkit_Database::get_table_name('history');

        $result = $wpdb->delete(
            $table_name,
            array('user_id' => $user_id),
            array('%d')
        );

        if ($result !== false) {
            wp_send_json_success(sprintf(__('Cleared %d history entries', 'ai-copytoolkit'), $result));
        } else {
            wp_send_json_error(__('Failed to clear history', 'ai-copytoolkit'));
        }
    }

    /**
     * Get single history item via AJAX
     */
    public function get_history_item() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }

        global $wpdb;

        $history_id = intval($_POST['history_id']);
        $user_id = get_current_user_id();
        $table_name = AI_CopyToolkit_Database::get_table_name('history');
        $templates_table = AI_CopyToolkit_Database::get_table_name('templates');

        $history_item = $wpdb->get_row($wpdb->prepare("
            SELECT h.*, t.name as template_name
            FROM $table_name h
            LEFT JOIN $templates_table t ON h.template_id = t.id
            WHERE h.id = %d AND h.user_id = %d
        ", $history_id, $user_id));

        if ($history_item) {
            wp_send_json_success($history_item);
        } else {
            wp_send_json_error(__('History item not found', 'ai-copytoolkit'));
        }
    }

    /**
     * Get logs via AJAX
     */
    public function get_logs() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }

        $level = isset($_POST['level']) ? sanitize_text_field($_POST['level']) : null;
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 100;

        $logger = AI_CopyToolkit_Logger::instance();
        $logs = $logger->get_recent_logs($limit, $level);

        wp_send_json_success($logs);
    }

    /**
     * Save default generation settings
     */
    public function save_default_settings() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-copytoolkit'));
        }

        $settings = isset($_POST['settings']) ? $_POST['settings'] : array();

        // Sanitize settings
        $clean_settings = array();
        if (isset($settings['model'])) {
            $clean_settings['default_model'] = sanitize_text_field($settings['model']);
        }
        if (isset($settings['maxTokens'])) {
            $clean_settings['max_tokens'] = intval($settings['maxTokens']);
        }
        if (isset($settings['temperature'])) {
            $clean_settings['temperature'] = floatval($settings['temperature']);
        }

        // Get current settings and merge
        $current_settings = AI_CopyToolkit::get_settings();
        $updated_settings = array_merge($current_settings, $clean_settings);

        // Save settings
        $result = AI_CopyToolkit::update_settings($updated_settings);

        if ($result) {
            wp_send_json_success(__('Default settings saved successfully', 'ai-copytoolkit'));
        } else {
            wp_send_json_error(__('Failed to save default settings', 'ai-copytoolkit'));
        }
    }

    /**
     * Admin notices
     */
    public function admin_notices() {
        $settings = AI_CopyToolkit::get_settings();
        
        // Check if API key is configured
        if (empty($settings['api_key'])) {
            $settings_url = admin_url('admin.php?page=ai-copytoolkit-settings');
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p>' . sprintf(
                __('AI CopyToolkit: Please configure your OpenRouter API key in the <a href="%s">settings</a> to start generating content.', 'ai-copytoolkit'),
                $settings_url
            ) . '</p>';
            echo '</div>';
        }
    }
}
