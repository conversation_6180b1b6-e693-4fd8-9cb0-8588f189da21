# AI CopyToolkit – Smart Copywriting Assistant
## Complete User Guide

### Table of Contents
1. [Getting Started](#getting-started)
2. [Initial Setup](#initial-setup)
3. [Generating Content](#generating-content)
4. [Managing Templates](#managing-templates)
5. [History and Analytics](#history-and-analytics)
6. [Settings and Configuration](#settings-and-configuration)
7. [Troubleshooting](#troubleshooting)
8. [Best Practices](#best-practices)

---

## Getting Started

AI CopyToolkit is a powerful WordPress plugin that helps you generate high-converting marketing content using advanced AI models from OpenRouter. Whether you need product descriptions, ad copy, email subject lines, or social media posts, this plugin streamlines your content creation process.

### Key Features
- **Multi-Model AI Support**: Access to 20+ AI models including GPT-4, Claude 3, LLaMA, and more
- **Pre-built Templates**: Ready-to-use templates for different content types
- **Custom Prompts**: Create your own prompts for specific needs
- **Generation History**: Track and manage all your generated content
- **Export Functionality**: Export your content in various formats
- **Usage Analytics**: Monitor your API usage and costs

---

## Initial Setup

### Step 1: Plugin Installation
1. Upload the plugin files to `/wp-content/plugins/ai-copytoolkit/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Navigate to 'AI CopyToolkit' in your WordPress admin menu

### Step 2: OpenRouter API Configuration
1. Visit [OpenRouter](https://openrouter.ai/keys) to create an account
2. Generate your API key (free tier available)
3. Go to **AI CopyToolkit > Settings**
4. Enter your API key in the "OpenRouter API Key" field
5. Click "Test Connection" to verify your setup
6. Save your settings

### Step 3: Initial Configuration
Configure your default settings:
- **Default AI Model**: Choose your preferred model (GPT-3.5-turbo recommended for beginners)
- **Max Tokens**: Set the maximum length for generated content (500 is a good starting point)
- **Temperature**: Control creativity level (0.7 for balanced results)
- **Enable History**: Keep track of your generations (recommended)

---

## Generating Content

### Using Pre-built Templates

1. Navigate to **AI CopyToolkit > Generate Content**
2. Browse available templates by category:
   - **E-commerce**: Product descriptions, feature lists
   - **Advertising**: Facebook ads, Google ads, display copy
   - **Email Marketing**: Subject lines, email content
   - **Social Media**: Posts for various platforms
   - **Conversion**: Call-to-action buttons, landing page copy

3. Click on a template to select it
4. Fill in the required parameters:
   - **Product Name**: The item you're describing
   - **Target Audience**: Who you're writing for
   - **Tone**: Professional, casual, enthusiastic, etc.
   - **Key Features**: Main selling points
   - **Benefits**: How it helps customers

5. Adjust generation settings if needed:
   - **AI Model**: Different models excel at different tasks
   - **Max Tokens**: Longer content requires more tokens
   - **Temperature**: Higher values = more creative, lower = more focused

6. Click "Generate Content"

### Using Custom Prompts

1. Switch to the "Custom Prompt" tab
2. Write a detailed prompt describing what you want:
   ```
   Write a compelling product description for a wireless bluetooth headphone 
   targeting fitness enthusiasts. Emphasize the sweat-proof design, 
   long battery life, and secure fit. Use an energetic, motivational tone 
   and keep it under 150 words.
   ```
3. Configure your generation settings
4. Click "Generate Content"

### Working with Generated Content

Once content is generated:
- **Copy**: Click the copy button to add to clipboard
- **Regenerate**: Generate a new version with the same parameters
- **Save**: Save to your generation history
- **Edit**: Make manual adjustments as needed

---

## Managing Templates

### Viewing Templates
Navigate to **AI CopyToolkit > Templates** to see all available templates:
- **Default Templates**: Pre-built templates included with the plugin
- **Custom Templates**: Templates you've created
- **Filter Options**: Sort by category or search by name

### Creating Custom Templates

1. Click "Add New Template"
2. Fill in the template details:
   - **Name**: Descriptive name for your template
   - **Description**: What this template is used for
   - **Category**: Organize your templates
   - **Content Type**: The type of content this generates

3. Create your prompt template using parameters:
   ```
   Write a {content_type} for {product_name} targeting {target_audience}.
   
   Key features: {features}
   Tone: {tone}
   Length: {min_words}-{max_words} words
   
   Include a compelling call-to-action and highlight the main benefit.
   ```

4. Define parameters:
   - **{product_name}**: Text input, required
   - **{target_audience}**: Text input, required
   - **{features}**: Textarea, required
   - **{tone}**: Dropdown (professional, casual, enthusiastic)
   - **{content_type}**: Dropdown (ad copy, description, email)

5. Save your template

### Editing Templates
- Click "Edit" on any custom template
- Modify the prompt or parameters as needed
- Default templates cannot be edited but can be duplicated

### Template Best Practices
- Use clear, descriptive parameter names
- Provide good default values
- Include specific instructions in your prompts
- Test your templates with different inputs
- Use consistent naming conventions

---

## History and Analytics

### Viewing Generation History
Navigate to **AI CopyToolkit > History** to see all your generated content:
- **Recent Generations**: Latest content with timestamps
- **Filter Options**: By date, model, or status
- **Search**: Find specific generations
- **Detailed View**: Click any item to see full details

### Exporting History
1. Click "Export History"
2. Choose your format (CSV or JSON)
3. Download the file containing all your generations

### Managing History
- **Delete Individual Items**: Remove specific generations
- **Clear All History**: Remove all history (cannot be undone)
- **History Limits**: Configure in settings to manage storage

---

## Settings and Configuration

### API Settings
- **API Key**: Your OpenRouter authentication key
- **Test Connection**: Verify your API key works
- **Model Selection**: Choose default AI model

### Generation Defaults
- **Max Tokens**: Default maximum length (50-4000)
- **Temperature**: Default creativity level (0.0-1.0)
- **Model**: Default AI model for new generations

### History Settings
- **Enable History**: Turn history tracking on/off
- **History Limit**: Maximum number of entries to keep
- **Auto-cleanup**: Automatically remove old entries

### Advanced Settings
- **Enable Logging**: Turn on detailed error logging
- **Debug Mode**: Additional debugging information
- **Rate Limiting**: Control API request frequency

---

## Troubleshooting

### Common Issues

**"API key not configured" error**
- Ensure you've entered your OpenRouter API key
- Test the connection in settings
- Check that your API key is active on OpenRouter

**"Rate limit exceeded" error**
- You've made too many requests too quickly
- Wait a few minutes before trying again
- Consider upgrading your OpenRouter plan

**"No response received" error**
- Check your internet connection
- Verify the OpenRouter service is operational
- Try a different AI model

**Generated content is poor quality**
- Adjust the temperature setting
- Try a different AI model
- Improve your prompt with more specific instructions
- Add more context and examples

### Getting Help
1. Check the system logs in **AI CopyToolkit > System Logs**
2. Review error messages for specific guidance
3. Test with different models and settings
4. Contact support with specific error details

---

## Best Practices

### Writing Effective Prompts
1. **Be Specific**: Include details about tone, length, audience
2. **Provide Context**: Explain the purpose and use case
3. **Use Examples**: Show the AI what you want
4. **Set Constraints**: Specify word limits, format requirements
5. **Iterate**: Refine prompts based on results

### Choosing AI Models
- **GPT-3.5-turbo**: Fast, cost-effective, good for most tasks
- **GPT-4**: Higher quality, better reasoning, more expensive
- **Claude 3**: Excellent for creative writing and analysis
- **LLaMA**: Good balance of quality and cost
- **Specialized Models**: Some excel at specific content types

### Managing Costs
- Start with free tier models
- Monitor your usage in the dashboard
- Set appropriate token limits
- Use history to avoid regenerating similar content
- Choose the right model for each task

### Content Quality Tips
- Always review and edit generated content
- Fact-check any claims or statistics
- Ensure brand voice consistency
- Test different approaches for the same content
- Keep successful prompts as templates

### Security and Privacy
- Keep your API key secure and private
- Don't include sensitive information in prompts
- Regularly review your generation history
- Use appropriate user permissions
- Monitor system logs for unusual activity

---

## Advanced Features

### Bulk Content Generation
- Create multiple variations of the same content
- Use templates with different parameter sets
- Export results for further processing

### Integration with Other Tools
- Copy generated content to your favorite editor
- Export to CSV for spreadsheet analysis
- Use with email marketing platforms

### Custom Workflows
- Create template libraries for different clients
- Develop standardized content processes
- Build approval workflows for team use

---

For additional support or feature requests, please contact our support team or visit our documentation website.
