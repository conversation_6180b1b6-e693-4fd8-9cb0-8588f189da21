<?php
/**
 * Generation history page
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap ai-copytoolkit-history">
    <h1 class="wp-heading-inline">
        <?php _e('Generation History', 'ai-copytoolkit'); ?>
    </h1>
    
    <div class="ai-copytoolkit-history-container">
        <!-- History Filters -->
        <div class="ai-copytoolkit-card history-filters">
            <div class="filter-group">
                <label for="date-filter"><?php _e('Filter by Date:', 'ai-copytoolkit'); ?></label>
                <select id="date-filter">
                    <option value=""><?php _e('All Time', 'ai-copytoolkit'); ?></option>
                    <option value="today"><?php _e('Today', 'ai-copytoolkit'); ?></option>
                    <option value="week"><?php _e('This Week', 'ai-copytoolkit'); ?></option>
                    <option value="month"><?php _e('This Month', 'ai-copytoolkit'); ?></option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="model-filter"><?php _e('Filter by Model:', 'ai-copytoolkit'); ?></label>
                <select id="model-filter">
                    <option value=""><?php _e('All Models', 'ai-copytoolkit'); ?></option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="status-filter"><?php _e('Filter by Status:', 'ai-copytoolkit'); ?></label>
                <select id="status-filter">
                    <option value=""><?php _e('All Status', 'ai-copytoolkit'); ?></option>
                    <option value="completed"><?php _e('Completed', 'ai-copytoolkit'); ?></option>
                    <option value="failed"><?php _e('Failed', 'ai-copytoolkit'); ?></option>
                </select>
            </div>
            
            <div class="filter-actions">
                <button id="export-history" class="button button-secondary">
                    <span class="dashicons dashicons-download"></span>
                    <?php _e('Export History', 'ai-copytoolkit'); ?>
                </button>
                <button id="clear-history" class="button button-link-delete">
                    <span class="dashicons dashicons-trash"></span>
                    <?php _e('Clear All History', 'ai-copytoolkit'); ?>
                </button>
            </div>
        </div>
        
        <!-- History List -->
        <div class="ai-copytoolkit-card history-list">
            <div class="history-header">
                <h2><?php _e('Your Generation History', 'ai-copytoolkit'); ?></h2>
                <div class="history-stats">
                    <span id="total-generations"><?php _e('Loading...', 'ai-copytoolkit'); ?></span>
                </div>
            </div>
            
            <div id="history-items" class="history-items">
                <div class="loading-spinner">
                    <span class="spinner is-active"></span>
                    <?php _e('Loading history...', 'ai-copytoolkit'); ?>
                </div>
            </div>
            
            <div class="history-pagination">
                <button id="load-more" class="button button-secondary" style="display: none;">
                    <?php _e('Load More', 'ai-copytoolkit'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- History Item Modal -->
<div id="history-modal" class="ai-copytoolkit-modal" style="display: none;">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h2 id="history-modal-title"><?php _e('Generation Details', 'ai-copytoolkit'); ?></h2>
            <button class="modal-close">&times;</button>
        </div>
        
        <div class="modal-body">
            <div class="history-details">
                <div class="detail-section">
                    <h3><?php _e('Generation Info', 'ai-copytoolkit'); ?></h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <strong><?php _e('Template:', 'ai-copytoolkit'); ?></strong>
                            <span id="detail-template"></span>
                        </div>
                        <div class="detail-item">
                            <strong><?php _e('Model:', 'ai-copytoolkit'); ?></strong>
                            <span id="detail-model"></span>
                        </div>
                        <div class="detail-item">
                            <strong><?php _e('Date:', 'ai-copytoolkit'); ?></strong>
                            <span id="detail-date"></span>
                        </div>
                        <div class="detail-item">
                            <strong><?php _e('Tokens Used:', 'ai-copytoolkit'); ?></strong>
                            <span id="detail-tokens"></span>
                        </div>
                        <div class="detail-item">
                            <strong><?php _e('Generation Time:', 'ai-copytoolkit'); ?></strong>
                            <span id="detail-time"></span>
                        </div>
                        <div class="detail-item">
                            <strong><?php _e('Status:', 'ai-copytoolkit'); ?></strong>
                            <span id="detail-status"></span>
                        </div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h3><?php _e('Prompt Used', 'ai-copytoolkit'); ?></h3>
                    <div class="prompt-content">
                        <textarea id="detail-prompt" readonly rows="6"></textarea>
                        <button class="copy-button" data-target="detail-prompt">
                            <span class="dashicons dashicons-admin-page"></span>
                            <?php _e('Copy', 'ai-copytoolkit'); ?>
                        </button>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h3><?php _e('Generated Content', 'ai-copytoolkit'); ?></h3>
                    <div class="content-area">
                        <textarea id="detail-content" readonly rows="10"></textarea>
                        <button class="copy-button" data-target="detail-content">
                            <span class="dashicons dashicons-admin-page"></span>
                            <?php _e('Copy', 'ai-copytoolkit'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="button button-secondary" id="history-modal-close"><?php _e('Close', 'ai-copytoolkit'); ?></button>
            <button type="button" class="button button-primary" id="regenerate-from-history"><?php _e('Regenerate', 'ai-copytoolkit'); ?></button>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    let currentPage = 1;
    let totalPages = 1;
    let currentHistoryId = null;
    
    // Load initial history
    loadHistory();
    
    // Filter handlers
    $('#date-filter, #model-filter, #status-filter').on('change', function() {
        currentPage = 1;
        loadHistory();
    });
    
    // Load more button
    $('#load-more').on('click', function() {
        currentPage++;
        loadHistory(true);
    });
    
    // Export history
    $('#export-history').on('click', function() {
        exportHistory();
    });
    
    // Clear history
    $('#clear-history').on('click', function() {
        if (confirm('<?php _e('Are you sure you want to clear all history? This action cannot be undone.', 'ai-copytoolkit'); ?>')) {
            clearHistory();
        }
    });
    
    // Modal controls
    $('.modal-close, #history-modal-close').on('click', function() {
        $('#history-modal').hide();
    });
    
    // Copy buttons
    $(document).on('click', '.copy-button', function() {
        const target = $(this).data('target');
        const content = $('#' + target).val();
        navigator.clipboard.writeText(content).then(function() {
            showNotice(aiCopyToolkit.strings.copied, 'success');
        }).catch(function() {
            showNotice(aiCopyToolkit.strings.copy_failed, 'error');
        });
    });
    
    // Regenerate from history
    $('#regenerate-from-history').on('click', function() {
        if (currentHistoryId) {
            window.location.href = '<?php echo admin_url('admin.php?page=ai-copytoolkit-generate'); ?>&history=' + currentHistoryId;
        }
    });
    
    function loadHistory(append = false) {
        if (!append) {
            $('#history-items').html('<div class="loading-spinner"><span class="spinner is-active"></span> <?php _e('Loading history...', 'ai-copytoolkit'); ?></div>');
        }
        
        const filters = {
            date: $('#date-filter').val(),
            model: $('#model-filter').val(),
            status: $('#status-filter').val()
        };
        
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_get_history',
            nonce: aiCopyToolkit.nonce,
            page: currentPage,
            per_page: 20,
            filters: filters
        }, function(response) {
            if (response.success) {
                displayHistory(response.data, append);
            } else {
                showNotice(response.data || '<?php _e('Failed to load history', 'ai-copytoolkit'); ?>', 'error');
            }
        });
    }
    
    function displayHistory(data, append) {
        const $container = $('#history-items');
        
        if (!append) {
            $container.empty();
        }
        
        if (data.history.length === 0 && !append) {
            $container.html('<div class="no-history"><p><?php _e('No generation history found.', 'ai-copytoolkit'); ?></p></div>');
            $('#load-more').hide();
            return;
        }
        
        data.history.forEach(function(item) {
            const $item = $('<div class="history-item" data-history-id="' + item.id + '">');
            
            $item.html(`
                <div class="history-header">
                    <div class="history-title">
                        <strong>${item.template_name || '<?php _e('Custom Prompt', 'ai-copytoolkit'); ?>'}</strong>
                        <span class="history-date">${formatDate(item.created_at)}</span>
                    </div>
                    <div class="history-status status-${item.status}">
                        ${item.status}
                    </div>
                </div>
                <div class="history-content">
                    ${truncateText(item.generated_content, 150)}
                </div>
                <div class="history-meta">
                    <span class="model-used">${item.model_used}</span>
                    <span class="tokens-used">${item.tokens_used} tokens</span>
                    <span class="generation-time">${parseFloat(item.generation_time).toFixed(2)}s</span>
                </div>
                <div class="history-actions">
                    <button class="button button-small view-history" data-history-id="${item.id}">
                        <?php _e('View', 'ai-copytoolkit'); ?>
                    </button>
                    <button class="button button-small copy-content" data-content="${escapeHtml(item.generated_content)}">
                        <?php _e('Copy', 'ai-copytoolkit'); ?>
                    </button>
                    <button class="button button-small button-link-delete delete-history" data-history-id="${item.id}">
                        <?php _e('Delete', 'ai-copytoolkit'); ?>
                    </button>
                </div>
            `);
            
            $container.append($item);
        });
        
        // Update pagination
        totalPages = data.total_pages;
        $('#total-generations').text(data.total + ' <?php _e('generations', 'ai-copytoolkit'); ?>');
        
        if (currentPage < totalPages) {
            $('#load-more').show();
        } else {
            $('#load-more').hide();
        }
        
        // Bind event handlers
        $('.view-history').off('click').on('click', function() {
            const historyId = $(this).data('history-id');
            viewHistoryDetails(historyId);
        });
        
        $('.copy-content').off('click').on('click', function() {
            const content = $(this).data('content');
            navigator.clipboard.writeText(content).then(function() {
                showNotice(aiCopyToolkit.strings.copied, 'success');
            });
        });
        
        $('.delete-history').off('click').on('click', function() {
            if (confirm(aiCopyToolkit.strings.confirm_delete)) {
                const historyId = $(this).data('history-id');
                deleteHistoryItem(historyId);
            }
        });
    }
    
    function viewHistoryDetails(historyId) {
        currentHistoryId = historyId;

        // Show loading state
        $('#history-modal').show();
        $('#detail-template').text('Loading...');
        $('#detail-model').text('Loading...');
        $('#detail-date').text('Loading...');
        $('#detail-tokens').text('Loading...');
        $('#detail-time').text('Loading...');
        $('#detail-status').text('Loading...');
        $('#detail-prompt').val('Loading...');
        $('#detail-content').val('Loading...');

        // Fetch detailed data from server
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_get_history_item',
            nonce: aiCopyToolkit.nonce,
            history_id: historyId
        }, function(response) {
            if (response.success) {
                const historyData = response.data;
                $('#detail-template').text(historyData.template_name || '<?php _e('Custom Prompt', 'ai-copytoolkit'); ?>');
                $('#detail-model').text(historyData.model_used);
                $('#detail-date').text(formatDate(historyData.created_at));
                $('#detail-tokens').text(historyData.tokens_used);
                $('#detail-time').text(parseFloat(historyData.generation_time).toFixed(2) + 's');
                $('#detail-status').text(historyData.status);
                $('#detail-prompt').val(historyData.prompt_text);
                $('#detail-content').val(historyData.generated_content);
            } else {
                showNotice(response.data || '<?php _e('Failed to load history details', 'ai-copytoolkit'); ?>', 'error');
                $('#history-modal').hide();
            }
        }).fail(function() {
            showNotice('<?php _e('Failed to load history details', 'ai-copytoolkit'); ?>', 'error');
            $('#history-modal').hide();
        });
    }

    function findHistoryItem(historyId) {
        // Find the item in the currently loaded history data
        let foundItem = null;
        $('.history-item').each(function() {
            if ($(this).data('history-id') == historyId) {
                // Extract data from the DOM element
                foundItem = {
                    template_name: $(this).find('.history-title strong').text(),
                    model_used: $(this).find('.model-used').text(),
                    created_at: $(this).find('.history-date').text(),
                    tokens_used: $(this).find('.tokens-used').text().replace(' tokens', ''),
                    generation_time: $(this).find('.generation-time').text().replace('s', ''),
                    status: $(this).find('.history-status').text(),
                    prompt_text: 'Prompt details would be loaded from server...',
                    generated_content: $(this).find('.history-content').text()
                };
                return false; // Break the loop
            }
        });

        return foundItem;
    }
    
    function deleteHistoryItem(historyId) {
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_delete_history',
            nonce: aiCopyToolkit.nonce,
            history_id: historyId
        }, function(response) {
            if (response.success) {
                $('[data-history-id="' + historyId + '"]').fadeOut();
                showNotice('<?php _e('History item deleted', 'ai-copytoolkit'); ?>', 'success');
            } else {
                showNotice(response.data || '<?php _e('Failed to delete history item', 'ai-copytoolkit'); ?>', 'error');
            }
        });
    }
    
    function exportHistory() {
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_export_history',
            nonce: aiCopyToolkit.nonce,
            format: 'csv'
        }, function(response) {
            if (response.success) {
                downloadFile(response.data.data, response.data.filename, 'text/csv');
                showNotice('<?php _e('History exported successfully', 'ai-copytoolkit'); ?>', 'success');
            } else {
                showNotice(response.data || '<?php _e('Failed to export history', 'ai-copytoolkit'); ?>', 'error');
            }
        });
    }
    
    function clearHistory() {
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_clear_all_history',
            nonce: aiCopyToolkit.nonce
        }, function(response) {
            if (response.success) {
                showNotice(response.data, 'success');
                loadHistory(); // Reload history
            } else {
                showNotice(response.data || '<?php _e('Failed to clear history', 'ai-copytoolkit'); ?>', 'error');
            }
        });
    }
    
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
    
    function truncateText(text, length) {
        if (text.length <= length) return text;
        return text.substring(0, length) + '...';
    }
    
    function escapeHtml(text) {
        return text.replace(/"/g, '&quot;').replace(/'/g, '&#39;');
    }
    
    function downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }
    
    function showNotice(message, type) {
        const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        $('.ai-copytoolkit-history .wp-heading-inline').after($notice);
        
        setTimeout(function() {
            $notice.fadeOut();
        }, 3000);
    }
});
</script>
