# AI CopyToolkit – Smart Copywriting Assistant

A powerful WordPress plugin that enables users to generate high-converting marketing content using AI models from OpenRouter.

## Features

- **Multi-Model AI Support**: Access to all OpenRouter AI models including DeepSeek, Meta LLaMA, Mistral, Claude 3, GPT-4o, and more
- **Content Generation**: Create product descriptions, Facebook/Google ad copy, email subject lines, CTAs, and social media posts
- **Pre-built Templates**: Ready-to-use copywriting templates for different content types
- **Generation History**: Track, save, and export all generated content
- **Customizable Parameters**: Control tone, length, target audience, and more
- **Responsive Design**: Mobile-friendly interface
- **Security First**: Follows WordPress security best practices

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- OpenRouter API key (free tier available)

## Installation

1. Upload the plugin files to `/wp-content/plugins/ai-copytoolkit/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Navigate to 'AI CopyToolkit' in your WordPress admin menu
4. Enter your OpenRouter API key in the settings
5. Start generating content!

## Usage

### Getting Started
1. Obtain an API key from [OpenRouter](https://openrouter.ai/)
2. Configure the plugin settings with your API key
3. Choose from pre-built templates or create custom prompts
4. Generate and refine your marketing content

### Content Types
- **Product Descriptions**: SEO-optimized product descriptions
- **Ad Copy**: Facebook and Google Ads copy
- **Email Marketing**: Subject lines and email content
- **Social Media**: Posts for various platforms
- **Call-to-Actions**: Compelling CTAs for better conversions

## Configuration

### API Settings
- **API Key**: Your OpenRouter API key
- **Default Model**: Choose your preferred AI model
- **Max Tokens**: Control response length
- **Temperature**: Adjust creativity level

### Content Settings
- **Enable History**: Save generation history
- **History Limit**: Number of generations to keep
- **Enable Logging**: Debug and error logging

## File Structure

```
ai-copytoolkit/
├── ai-copytoolkit.php          # Main plugin file
├── includes/                   # Core classes
├── admin/                      # Admin interface
├── assets/                     # CSS, JS, images
├── languages/                  # Translation files
├── templates/                  # Content templates
└── README.md                   # Documentation
```

## Development

### Coding Standards
- Follows WordPress Coding Standards
- PSR-4 autoloading
- Comprehensive error handling
- Security best practices

### Hooks and Filters
The plugin provides various hooks and filters for extensibility:

- `ai_copytoolkit_loaded` - Plugin initialization
- `ai_copytoolkit_before_generation` - Before content generation
- `ai_copytoolkit_after_generation` - After content generation
- `ai_copytoolkit_settings` - Filter plugin settings

## Support

For support and documentation, please visit our website or contact our support team.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### 1.0.0
- Initial release
- OpenRouter API integration
- Content generation templates
- Admin dashboard
- Generation history
- Settings management
