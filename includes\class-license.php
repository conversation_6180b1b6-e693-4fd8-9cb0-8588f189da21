<?php
/**
 * License management class
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI_CopyToolkit_License class
 */
class AI_CopyToolkit_License {
    
    /**
     * Instance
     *
     * @var AI_CopyToolkit_License
     */
    private static $instance = null;
    
    /**
     * License server URL
     *
     * @var string
     */
    private $license_server = 'https://api.example.com/licenses/';

    /**
     * Valid license keys (for simple validation)
     *
     * @var array
     */
    private $valid_licenses = array(
        'DEMO-LICENSE-KEY-2024' => array(
            'status' => 'valid',
            'expires' => 'lifetime',
            'customer_name' => 'Demo User',
            'customer_email' => '<EMAIL>',
            'activations_left' => 999
        ),
        'TEST-LICENSE-12345' => array(
            'status' => 'valid',
            'expires' => '2025-12-31',
            'customer_name' => 'Test User',
            'customer_email' => '<EMAIL>',
            'activations_left' => 5
        )
    );
    
    /**
     * Product ID
     *
     * @var string
     */
    private $product_id = 'ai-copytoolkit';
    
    /**
     * License statuses
     */
    const STATUS_VALID = 'valid';
    const STATUS_INVALID = 'invalid';
    const STATUS_EXPIRED = 'expired';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_INACTIVE = 'inactive';
    
    /**
     * Get instance
     *
     * @return AI_CopyToolkit_License
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_init', array($this, 'check_license_status'));
        add_action('wp_ajax_ai_copytoolkit_activate_license', array($this, 'activate_license'));
        add_action('wp_ajax_ai_copytoolkit_deactivate_license', array($this, 'deactivate_license'));
        add_action('wp_ajax_ai_copytoolkit_check_license', array($this, 'check_license'));
        
        // Daily license check
        add_action('ai_copytoolkit_daily_license_check', array($this, 'daily_license_check'));
        if (!wp_next_scheduled('ai_copytoolkit_daily_license_check')) {
            wp_schedule_event(time(), 'daily', 'ai_copytoolkit_daily_license_check');
        }
        
        // Admin notices
        add_action('admin_notices', array($this, 'license_admin_notices'));
    }
    
    /**
     * Get license key
     *
     * @return string
     */
    public function get_license_key() {
        return get_option('ai_copytoolkit_license_key', '');
    }
    
    /**
     * Get license status
     *
     * @return string
     */
    public function get_license_status() {
        return get_option('ai_copytoolkit_license_status', self::STATUS_INACTIVE);
    }
    
    /**
     * Get license data
     *
     * @return array
     */
    public function get_license_data() {
        return get_option('ai_copytoolkit_license_data', array());
    }
    
    /**
     * Check if license is valid
     *
     * @return bool
     */
    public function is_license_valid() {
        // Allow bypass for development/testing
        if (defined('AI_COPYTOOLKIT_BYPASS_LICENSE') && AI_COPYTOOLKIT_BYPASS_LICENSE) {
            return true;
        }

        $status = $this->get_license_status();
        return $status === self::STATUS_VALID;
    }
    
    /**
     * Activate license
     */
    public function activate_license() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        $license_key = sanitize_text_field($_POST['license_key']);
        
        if (empty($license_key)) {
            wp_send_json_error(__('License key is required', 'ai-copytoolkit'));
        }
        
        $result = $this->activate_license_remote($license_key);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        // Save license data
        update_option('ai_copytoolkit_license_key', $license_key);
        update_option('ai_copytoolkit_license_status', $result['status']);
        update_option('ai_copytoolkit_license_data', $result);
        update_option('ai_copytoolkit_license_last_check', time());
        
        wp_send_json_success(array(
            'message' => __('License activated successfully', 'ai-copytoolkit'),
            'status' => $result['status'],
            'expires' => isset($result['expires']) ? $result['expires'] : null
        ));
    }
    
    /**
     * Deactivate license
     */
    public function deactivate_license() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        $license_key = $this->get_license_key();
        
        if (empty($license_key)) {
            wp_send_json_error(__('No license key found', 'ai-copytoolkit'));
        }
        
        $result = $this->deactivate_license_remote($license_key);
        
        // Clear license data regardless of remote result
        delete_option('ai_copytoolkit_license_key');
        delete_option('ai_copytoolkit_license_status');
        delete_option('ai_copytoolkit_license_data');
        delete_option('ai_copytoolkit_license_last_check');
        
        if (is_wp_error($result)) {
            wp_send_json_success(array(
                'message' => __('License deactivated locally (remote deactivation failed)', 'ai-copytoolkit')
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('License deactivated successfully', 'ai-copytoolkit')
            ));
        }
    }
    
    /**
     * Check license via AJAX
     */
    public function check_license() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        $result = $this->check_license_remote();
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        // Update license data
        update_option('ai_copytoolkit_license_status', $result['status']);
        update_option('ai_copytoolkit_license_data', $result);
        update_option('ai_copytoolkit_license_last_check', time());
        
        wp_send_json_success($result);
    }
    
    /**
     * Activate license remotely
     *
     * @param string $license_key
     * @return array|WP_Error
     */
    private function activate_license_remote($license_key) {
        // First check local valid licenses
        if (isset($this->valid_licenses[$license_key])) {
            return $this->valid_licenses[$license_key];
        }

        // If not found locally, try remote server
        $api_params = array(
            'action' => 'activate_license',
            'license' => $license_key,
            'item_id' => $this->product_id,
            'url' => home_url(),
            'version' => AI_COPYTOOLKIT_VERSION
        );

        $result = $this->make_license_request($api_params);

        // If remote server fails, return error
        if (is_wp_error($result)) {
            return new WP_Error('invalid_license', __('Invalid license key. Please check your license key or contact support.', 'ai-copytoolkit'));
        }

        return $result;
    }
    
    /**
     * Deactivate license remotely
     *
     * @param string $license_key
     * @return array|WP_Error
     */
    private function deactivate_license_remote($license_key) {
        $api_params = array(
            'action' => 'deactivate_license',
            'license' => $license_key,
            'item_id' => $this->product_id,
            'url' => home_url()
        );
        
        return $this->make_license_request($api_params);
    }
    
    /**
     * Check license remotely
     *
     * @return array|WP_Error
     */
    private function check_license_remote() {
        $license_key = $this->get_license_key();

        if (empty($license_key)) {
            return new WP_Error('no_license', __('No license key found', 'ai-copytoolkit'));
        }

        // First check local valid licenses
        if (isset($this->valid_licenses[$license_key])) {
            return $this->valid_licenses[$license_key];
        }

        // If not found locally, try remote server
        $api_params = array(
            'action' => 'check_license',
            'license' => $license_key,
            'item_id' => $this->product_id,
            'url' => home_url()
        );

        $result = $this->make_license_request($api_params);

        // If remote server fails but we have a stored license, return stored status
        if (is_wp_error($result)) {
            $stored_status = $this->get_license_status();
            if ($stored_status === self::STATUS_VALID) {
                return array('status' => $stored_status);
            }
            return $result;
        }

        return $result;
    }
    
    /**
     * Make license server request
     *
     * @param array $api_params
     * @return array|WP_Error
     */
    private function make_license_request($api_params) {
        $response = wp_remote_post($this->license_server, array(
            'timeout' => 15,
            'sslverify' => true,
            'body' => $api_params
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            return new WP_Error('license_server_error', __('License server error', 'ai-copytoolkit'));
        }
        
        $license_data = json_decode($response_body, true);
        
        if (null === $license_data) {
            return new WP_Error('invalid_response', __('Invalid response from license server', 'ai-copytoolkit'));
        }
        
        return $license_data;
    }
    
    /**
     * Check license status on admin init
     */
    public function check_license_status() {
        // Only check on plugin pages
        if (!isset($_GET['page']) || strpos($_GET['page'], 'ai-copytoolkit') !== 0) {
            return;
        }
        
        $last_check = get_option('ai_copytoolkit_license_last_check', 0);
        $check_interval = 24 * HOUR_IN_SECONDS; // Check daily
        
        if ((time() - $last_check) > $check_interval) {
            $this->daily_license_check();
        }
    }
    
    /**
     * Daily license check
     */
    public function daily_license_check() {
        $license_key = $this->get_license_key();
        
        if (empty($license_key)) {
            return;
        }
        
        $result = $this->check_license_remote();
        
        if (!is_wp_error($result)) {
            update_option('ai_copytoolkit_license_status', $result['status']);
            update_option('ai_copytoolkit_license_data', $result);
        }
        
        update_option('ai_copytoolkit_license_last_check', time());
    }
    
    /**
     * Display license admin notices
     */
    public function license_admin_notices() {
        // Only show on plugin pages
        if (!isset($_GET['page']) || strpos($_GET['page'], 'ai-copytoolkit') !== 0) {
            return;
        }
        
        $status = $this->get_license_status();
        $license_data = $this->get_license_data();
        
        switch ($status) {
            case self::STATUS_EXPIRED:
                echo '<div class="notice notice-error">';
                echo '<p><strong>' . __('AI CopyToolkit License Expired', 'ai-copytoolkit') . '</strong></p>';
                echo '<p>' . __('Your license has expired. Please renew your license to continue receiving updates and support.', 'ai-copytoolkit') . '</p>';
                echo '</div>';
                break;
                
            case self::STATUS_INVALID:
                echo '<div class="notice notice-error">';
                echo '<p><strong>' . __('AI CopyToolkit License Invalid', 'ai-copytoolkit') . '</strong></p>';
                echo '<p>' . __('Your license key is invalid. Please check your license key or contact support.', 'ai-copytoolkit') . '</p>';
                echo '</div>';
                break;
                
            case self::STATUS_SUSPENDED:
                echo '<div class="notice notice-error">';
                echo '<p><strong>' . __('AI CopyToolkit License Suspended', 'ai-copytoolkit') . '</strong></p>';
                echo '<p>' . __('Your license has been suspended. Please contact support for assistance.', 'ai-copytoolkit') . '</p>';
                echo '</div>';
                break;
                
            case self::STATUS_INACTIVE:
                echo '<div class="notice notice-warning">';
                echo '<p><strong>' . __('AI CopyToolkit License Required', 'ai-copytoolkit') . '</strong></p>';
                echo '<p>' . sprintf(
                    __('Please <a href="%s">activate your license</a> to receive updates and support.', 'ai-copytoolkit'),
                    admin_url('admin.php?page=ai-copytoolkit-settings#license')
                ) . '</p>';
                echo '<p><strong>' . __('For Testing:', 'ai-copytoolkit') . '</strong> ' . __('Use license key', 'ai-copytoolkit') . ' <code>DEMO-LICENSE-KEY-2024</code> ' . __('or', 'ai-copytoolkit') . ' <code>TEST-LICENSE-12345</code></p>';
                echo '</div>';
                break;
                
            case self::STATUS_VALID:
                // Check if license is expiring soon
                if (isset($license_data['expires']) && $license_data['expires'] !== 'lifetime') {
                    $expires = strtotime($license_data['expires']);
                    $days_until_expiry = ceil(($expires - time()) / DAY_IN_SECONDS);
                    
                    if ($days_until_expiry <= 30 && $days_until_expiry > 0) {
                        echo '<div class="notice notice-warning">';
                        echo '<p><strong>' . __('AI CopyToolkit License Expiring Soon', 'ai-copytoolkit') . '</strong></p>';
                        echo '<p>' . sprintf(
                            __('Your license expires in %d days. Please renew to continue receiving updates.', 'ai-copytoolkit'),
                            $days_until_expiry
                        ) . '</p>';
                        echo '</div>';
                    }
                }
                break;
        }
    }
    
    /**
     * Get license info for display
     *
     * @return array
     */
    public function get_license_info() {
        $license_data = $this->get_license_data();
        $status = $this->get_license_status();
        
        $info = array(
            'status' => $status,
            'key' => $this->get_license_key(),
            'expires' => isset($license_data['expires']) ? $license_data['expires'] : null,
            'activations_left' => isset($license_data['activations_left']) ? $license_data['activations_left'] : null,
            'customer_name' => isset($license_data['customer_name']) ? $license_data['customer_name'] : null,
            'customer_email' => isset($license_data['customer_email']) ? $license_data['customer_email'] : null
        );
        
        return $info;
    }
}
