<?php
/**
 * WooCommerce Integration for AI CopyToolkit
 * 
 * This file implements deep WooCommerce integration for product descriptions
 * Priority: HIGH IMPACT - Targets 5M+ WooCommerce stores worldwide
 * 
 * @package AI_CopyToolkit
 * @version 1.1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI CopyToolkit WooCommerce Integration
 */
class AI_CopyToolkit_WooCommerce {
    
    /**
     * Initialize WooCommerce integration
     */
    public static function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            return;
        }
        
        // Product editor integration
        add_action('woocommerce_product_options_general_product_data', array(__CLASS__, 'add_product_ai_fields'));
        add_action('woocommerce_process_product_meta', array(__CLASS__, 'save_product_ai_data'));
        
        // Bulk actions
        add_filter('bulk_actions-edit-product', array(__CLASS__, 'add_bulk_actions'));
        add_filter('handle_bulk_actions-edit-product', array(__CLASS__, 'handle_bulk_actions'), 10, 3);
        
        // Admin scripts and styles
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_admin_scripts'));
        
        // AJAX handlers
        add_action('wp_ajax_ai_copytoolkit_generate_product_description', array(__CLASS__, 'ajax_generate_product_description'));
        add_action('wp_ajax_ai_copytoolkit_bulk_generate_descriptions', array(__CLASS__, 'ajax_bulk_generate_descriptions'));
        
        // Admin menu
        add_action('admin_menu', array(__CLASS__, 'add_admin_menu'));
    }
    
    /**
     * Add AI fields to product editor
     */
    public static function add_product_ai_fields() {
        global $post;
        
        echo '<div class="options_group ai-copytoolkit-product-options">';
        
        // AI Generation section
        echo '<div class="ai-copytoolkit-section">';
        echo '<h4>' . __('🤖 AI Content Generation', 'ai-copytoolkit') . '</h4>';
        
        // Product features input
        woocommerce_wp_textarea_input(array(
            'id' => '_ai_product_features',
            'label' => __('Product Features', 'ai-copytoolkit'),
            'placeholder' => __('Enter key product features (one per line)', 'ai-copytoolkit'),
            'description' => __('List the main features and benefits of this product', 'ai-copytoolkit'),
            'rows' => 4
        ));
        
        // Target audience
        woocommerce_wp_text_input(array(
            'id' => '_ai_target_audience',
            'label' => __('Target Audience', 'ai-copytoolkit'),
            'placeholder' => __('e.g., fitness enthusiasts, small business owners', 'ai-copytoolkit'),
            'description' => __('Who is this product for?', 'ai-copytoolkit')
        ));
        
        // Tone/Style
        woocommerce_wp_select(array(
            'id' => '_ai_tone',
            'label' => __('Writing Tone', 'ai-copytoolkit'),
            'options' => array(
                'professional' => __('Professional', 'ai-copytoolkit'),
                'friendly' => __('Friendly', 'ai-copytoolkit'),
                'enthusiastic' => __('Enthusiastic', 'ai-copytoolkit'),
                'technical' => __('Technical', 'ai-copytoolkit'),
                'luxury' => __('Luxury', 'ai-copytoolkit')
            ),
            'description' => __('Choose the writing style for your product description', 'ai-copytoolkit')
        ));
        
        // AI Model selection
        $models = self::get_available_models();
        woocommerce_wp_select(array(
            'id' => '_ai_model',
            'label' => __('AI Model', 'ai-copytoolkit'),
            'options' => $models,
            'description' => __('Choose the AI model for content generation', 'ai-copytoolkit')
        ));
        
        // Generation buttons
        echo '<p class="form-field ai-generation-buttons">';
        echo '<button type="button" id="generate-product-description" class="button button-primary">';
        echo __('Generate Description', 'ai-copytoolkit') . '</button>';
        echo '<button type="button" id="generate-short-description" class="button button-secondary">';
        echo __('Generate Short Description', 'ai-copytoolkit') . '</button>';
        echo '<button type="button" id="generate-seo-title" class="button button-secondary">';
        echo __('Generate SEO Title', 'ai-copytoolkit') . '</button>';
        echo '</p>';
        
        // Loading indicator
        echo '<div id="ai-generation-loading" style="display: none;">';
        echo '<span class="spinner is-active"></span> ' . __('Generating content...', 'ai-copytoolkit');
        echo '</div>';
        
        // Results area
        echo '<div id="ai-generation-results" style="display: none;"></div>';
        
        echo '</div>';
        echo '</div>';
    }
    
    /**
     * Save product AI data
     */
    public static function save_product_ai_data($post_id) {
        $fields = array('_ai_product_features', '_ai_target_audience', '_ai_tone', '_ai_model');
        
        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, $field, sanitize_textarea_field($_POST[$field]));
            }
        }
    }
    
    /**
     * Add bulk actions
     */
    public static function add_bulk_actions($actions) {
        $actions['ai_generate_descriptions'] = __('Generate AI Descriptions', 'ai-copytoolkit');
        $actions['ai_generate_short_descriptions'] = __('Generate AI Short Descriptions', 'ai-copytoolkit');
        return $actions;
    }
    
    /**
     * Handle bulk actions
     */
    public static function handle_bulk_actions($redirect_to, $action, $post_ids) {
        if ($action === 'ai_generate_descriptions' || $action === 'ai_generate_short_descriptions') {
            $processed = 0;
            
            foreach ($post_ids as $post_id) {
                $product = wc_get_product($post_id);
                if (!$product) continue;
                
                $content_type = ($action === 'ai_generate_descriptions') ? 'description' : 'short_description';
                $result = self::generate_product_content($product, $content_type);
                
                if (!is_wp_error($result)) {
                    $processed++;
                }
            }
            
            $redirect_to = add_query_arg('ai_bulk_generated', $processed, $redirect_to);
        }
        
        return $redirect_to;
    }
    
    /**
     * Enqueue admin scripts
     */
    public static function enqueue_admin_scripts($hook) {
        if ($hook !== 'post.php' && $hook !== 'post-new.php') {
            return;
        }
        
        global $post;
        if (!$post || $post->post_type !== 'product') {
            return;
        }
        
        wp_enqueue_script(
            'ai-copytoolkit-woocommerce',
            AI_COPYTOOLKIT_PLUGIN_URL . 'assets/js/woocommerce-integration.js',
            array('jquery'),
            AI_COPYTOOLKIT_VERSION,
            true
        );
        
        wp_localize_script('ai-copytoolkit-woocommerce', 'aiCopyToolkitWoo', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ai_copytoolkit_nonce'),
            'strings' => array(
                'generating' => __('Generating...', 'ai-copytoolkit'),
                'error' => __('Error generating content', 'ai-copytoolkit'),
                'success' => __('Content generated successfully!', 'ai-copytoolkit'),
                'insertContent' => __('Insert Content', 'ai-copytoolkit'),
                'regenerate' => __('Regenerate', 'ai-copytoolkit')
            )
        ));
        
        wp_enqueue_style(
            'ai-copytoolkit-woocommerce',
            AI_COPYTOOLKIT_PLUGIN_URL . 'assets/css/woocommerce-integration.css',
            array(),
            AI_COPYTOOLKIT_VERSION
        );
    }
    
    /**
     * AJAX handler for product description generation
     */
    public static function ajax_generate_product_description() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('edit_products')) {
            wp_send_json_error(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        $product_id = intval($_POST['product_id']);
        $content_type = sanitize_text_field($_POST['content_type']);
        
        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(__('Product not found', 'ai-copytoolkit'));
        }
        
        $result = self::generate_product_content($product, $content_type);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        wp_send_json_success(array(
            'content' => $result,
            'content_type' => $content_type
        ));
    }
    
    /**
     * Generate product content
     */
    private static function generate_product_content($product, $content_type) {
        $product_name = $product->get_name();
        $product_price = $product->get_price();
        $product_categories = wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'names'));
        
        // Get AI settings from product meta
        $features = get_post_meta($product->get_id(), '_ai_product_features', true);
        $target_audience = get_post_meta($product->get_id(), '_ai_target_audience', true);
        $tone = get_post_meta($product->get_id(), '_ai_tone', true) ?: 'professional';
        $model = get_post_meta($product->get_id(), '_ai_model', true) ?: 'openai/gpt-3.5-turbo';
        
        // Build prompt based on content type
        switch ($content_type) {
            case 'description':
                $prompt = self::build_description_prompt($product_name, $features, $target_audience, $tone, $product_categories);
                $max_tokens = 800;
                break;
                
            case 'short_description':
                $prompt = self::build_short_description_prompt($product_name, $features, $target_audience, $tone);
                $max_tokens = 200;
                break;
                
            case 'seo_title':
                $prompt = self::build_seo_title_prompt($product_name, $product_categories, $target_audience);
                $max_tokens = 100;
                break;
                
            default:
                return new WP_Error('invalid_type', __('Invalid content type', 'ai-copytoolkit'));
        }
        
        // Generate content
        $api = AI_CopyToolkit_API::instance();
        $result = $api->generate_content($prompt, array(
            'model' => $model,
            'max_tokens' => $max_tokens,
            'temperature' => 0.7
        ));
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        // Save to history
        AI_CopyToolkit_Database::instance()->save_generation(
            get_current_user_id(),
            'woocommerce_' . $content_type,
            $prompt,
            $result,
            $model,
            array('product_id' => $product->get_id())
        );
        
        return $result;
    }
    
    /**
     * Build description prompt
     */
    private static function build_description_prompt($product_name, $features, $target_audience, $tone, $categories) {
        $category_text = !empty($categories) ? implode(', ', $categories) : 'general product';
        
        $prompt = "Write a compelling product description for '{$product_name}', a {$category_text}.\n\n";
        
        if (!empty($target_audience)) {
            $prompt .= "Target audience: {$target_audience}\n";
        }
        
        if (!empty($features)) {
            $prompt .= "Key features:\n{$features}\n\n";
        }
        
        $prompt .= "Writing style: {$tone}\n\n";
        $prompt .= "Requirements:\n";
        $prompt .= "- Write in {$tone} tone\n";
        $prompt .= "- Focus on benefits, not just features\n";
        $prompt .= "- Include emotional triggers\n";
        $prompt .= "- Use persuasive language\n";
        $prompt .= "- Make it scannable with bullet points\n";
        $prompt .= "- Include a call to action\n";
        $prompt .= "- Optimize for SEO\n\n";
        $prompt .= "Product description:";
        
        return $prompt;
    }
    
    /**
     * Build short description prompt
     */
    private static function build_short_description_prompt($product_name, $features, $target_audience, $tone) {
        $prompt = "Write a short, compelling product summary for '{$product_name}'.\n\n";
        
        if (!empty($target_audience)) {
            $prompt .= "Target audience: {$target_audience}\n";
        }
        
        if (!empty($features)) {
            $prompt .= "Key features: {$features}\n\n";
        }
        
        $prompt .= "Requirements:\n";
        $prompt .= "- Maximum 2-3 sentences\n";
        $prompt .= "- {$tone} tone\n";
        $prompt .= "- Focus on main benefit\n";
        $prompt .= "- Create urgency or desire\n\n";
        $prompt .= "Short description:";
        
        return $prompt;
    }
    
    /**
     * Build SEO title prompt
     */
    private static function build_seo_title_prompt($product_name, $categories, $target_audience) {
        $category_text = !empty($categories) ? implode(', ', $categories) : '';
        
        $prompt = "Create an SEO-optimized title for '{$product_name}'.\n\n";
        
        if (!empty($category_text)) {
            $prompt .= "Product category: {$category_text}\n";
        }
        
        if (!empty($target_audience)) {
            $prompt .= "Target audience: {$target_audience}\n";
        }
        
        $prompt .= "\nRequirements:\n";
        $prompt .= "- Maximum 60 characters\n";
        $prompt .= "- Include relevant keywords\n";
        $prompt .= "- Be descriptive and compelling\n";
        $prompt .= "- Avoid keyword stuffing\n\n";
        $prompt .= "SEO title:";
        
        return $prompt;
    }
    
    /**
     * Get available models
     */
    private static function get_available_models() {
        $api = AI_CopyToolkit_API::instance();
        $models = $api->fetch_available_models();
        
        $options = array();
        
        if (is_wp_error($models)) {
            // Fallback models
            $options['openai/gpt-3.5-turbo'] = 'GPT-3.5 Turbo';
            $options['anthropic/claude-3-haiku'] = 'Claude 3 Haiku';
        } else {
            foreach (array_slice($models, 0, 10) as $model) {
                $options[$model['id']] = $model['name'] ?? $model['id'];
            }
        }
        
        return $options;
    }
    
    /**
     * Add admin menu
     */
    public static function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('AI Content Generator', 'ai-copytoolkit'),
            __('AI Content', 'ai-copytoolkit'),
            'manage_woocommerce',
            'ai-copytoolkit-woocommerce',
            array(__CLASS__, 'admin_page')
        );
    }
    
    /**
     * Admin page for bulk operations
     */
    public static function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('AI Content Generator for WooCommerce', 'ai-copytoolkit'); ?></h1>
            
            <div class="ai-copytoolkit-woo-dashboard">
                <div class="postbox">
                    <h2 class="hndle"><?php _e('Bulk Content Generation', 'ai-copytoolkit'); ?></h2>
                    <div class="inside">
                        <p><?php _e('Generate AI-powered content for multiple products at once.', 'ai-copytoolkit'); ?></p>
                        
                        <form id="bulk-generation-form">
                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('Product Categories', 'ai-copytoolkit'); ?></th>
                                    <td>
                                        <?php
                                        $categories = get_terms(array(
                                            'taxonomy' => 'product_cat',
                                            'hide_empty' => false
                                        ));
                                        
                                        foreach ($categories as $category) {
                                            echo '<label><input type="checkbox" name="categories[]" value="' . $category->term_id . '"> ' . $category->name . '</label><br>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row"><?php _e('Content Type', 'ai-copytoolkit'); ?></th>
                                    <td>
                                        <select name="content_type">
                                            <option value="description"><?php _e('Product Descriptions', 'ai-copytoolkit'); ?></option>
                                            <option value="short_description"><?php _e('Short Descriptions', 'ai-copytoolkit'); ?></option>
                                            <option value="seo_title"><?php _e('SEO Titles', 'ai-copytoolkit'); ?></option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row"><?php _e('AI Model', 'ai-copytoolkit'); ?></th>
                                    <td>
                                        <select name="ai_model">
                                            <?php foreach (self::get_available_models() as $id => $name): ?>
                                                <option value="<?php echo esc_attr($id); ?>"><?php echo esc_html($name); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </td>
                                </tr>
                            </table>
                            
                            <p class="submit">
                                <button type="submit" class="button button-primary"><?php _e('Generate Content', 'ai-copytoolkit'); ?></button>
                            </p>
                        </form>
                        
                        <div id="bulk-generation-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <p class="progress-text"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
}

// Initialize WooCommerce integration
AI_CopyToolkit_WooCommerce::init();
?>
