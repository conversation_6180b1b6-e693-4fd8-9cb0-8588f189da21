<?php
/**
 * Plugin Name: AI CopyToolkit – Smart Copywriting Assistant
 * Plugin URI: https://example.com/ai-copytoolkit
 * Description: Generate high-converting marketing content using AI models from OpenRouter. Create product descriptions, ad copy, email subject lines, CTAs, and social media posts with ease.
 * Version: 1.0.0
 * Author: Your Company Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: ai-copytoolkit
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package AI_CopyToolkit
 * @version 1.0.0
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('AI_COPYTOOLKIT_VERSION', '1.0.0');
define('AI_COPYTOOLKIT_PLUGIN_FILE', __FILE__);
define('AI_COPYTOOLKIT_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('AI_COPYTOOLKIT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('AI_COPYTOOLKIT_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('AI_COPYTOOLKIT_TABLE_PREFIX', 'ai_copytoolkit_');

// Development/Testing: Uncomment the line below to bypass license requirement
// define('AI_COPYTOOLKIT_BYPASS_LICENSE', true);

// Ensure the database class is loaded for activation and other static calls
require_once AI_COPYTOOLKIT_PLUGIN_DIR . 'includes/class-database.php';

/**
 * Main plugin class
 */
final class AI_CopyToolkit {
    
    /**
     * Plugin instance
     *
     * @var AI_CopyToolkit
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     *
     * @return AI_CopyToolkit
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'), 0);
        add_action('plugins_loaded', array($this, 'plugins_loaded'));
        
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('AI_CopyToolkit', 'uninstall'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain
        load_plugin_textdomain('ai-copytoolkit', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Initialize autoloader
        $this->init_autoloader();
        
        // Initialize plugin components
        $this->init_components();
    }
    
    /**
     * Initialize autoloader
     */
    private function init_autoloader() {
        spl_autoload_register(array($this, 'autoload'));
    }
    
    /**
     * Autoload classes
     *
     * @param string $class_name
     */
    public function autoload($class_name) {
        if (strpos($class_name, 'AI_CopyToolkit') !== 0) {
            return;
        }
        
        $class_file = str_replace('_', '-', strtolower($class_name));
        $class_file = str_replace('ai-copytoolkit-', '', $class_file);
        
        $file_path = AI_COPYTOOLKIT_PLUGIN_DIR . 'includes/class-' . $class_file . '.php';
        
        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        // Initialize security
        AI_CopyToolkit_Security::instance();

        // Initialize logger
        AI_CopyToolkit_Logger::instance();

        // Initialize license management
        AI_CopyToolkit_License::instance();

        // Initialize database
        AI_CopyToolkit_Database::instance();

        // Initialize admin
        if (is_admin()) {
            AI_CopyToolkit_Admin::instance();
        }

        // Initialize API
        AI_CopyToolkit_API::instance();

        // Initialize content generator
        AI_CopyToolkit_Content_Generator::instance();
    }
    
    /**
     * Plugin loaded
     */
    public function plugins_loaded() {
        do_action('ai_copytoolkit_loaded');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        AI_CopyToolkit_Database::create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        do_action('ai_copytoolkit_activated');
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('ai_copytoolkit_cleanup');
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        do_action('ai_copytoolkit_deactivated');
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove database tables
        AI_CopyToolkit_Database::drop_tables();
        
        // Remove options
        delete_option('ai_copytoolkit_settings');
        delete_option('ai_copytoolkit_version');
        
        do_action('ai_copytoolkit_uninstalled');
    }
    
    /**
     * Set default options
     */
    private function set_default_options() {
        $default_settings = array(
            'api_key' => '',
            'default_model' => 'openai/gpt-3.5-turbo',
            'max_tokens' => 500,
            'temperature' => 0.7,
            'enable_history' => true,
            'history_limit' => 100,
            'enable_logging' => true,
        );
        
        add_option('ai_copytoolkit_settings', $default_settings);
        add_option('ai_copytoolkit_version', AI_COPYTOOLKIT_VERSION);
    }
    
    /**
     * Get plugin settings
     *
     * @return array
     */
    public static function get_settings() {
        return get_option('ai_copytoolkit_settings', array());
    }
    
    /**
     * Update plugin settings
     *
     * @param array $settings
     * @return bool
     */
    public static function update_settings($settings) {
        return update_option('ai_copytoolkit_settings', $settings);
    }
}

/**
 * Initialize the plugin
 */
function ai_copytoolkit() {
    return AI_CopyToolkit::instance();
}

// Initialize plugin
ai_copytoolkit();
