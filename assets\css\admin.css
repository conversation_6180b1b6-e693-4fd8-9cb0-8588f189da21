/**
 * AI CopyToolkit Admin Styles
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

/* ==========================================================================
   Base Styles
   ========================================================================== */

.ai-copytoolkit-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.ai-copytoolkit-card h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
    color: #23282d;
}

.ai-copytoolkit-card h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
}

/* ==========================================================================
   Dashboard Styles
   ========================================================================== */

.ai-copytoolkit-dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.ai-copytoolkit-stats .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ai-copytoolkit-quick-actions .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.ai-copytoolkit-quick-actions .button {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: flex-start;
    padding: 12px 16px;
    text-decoration: none;
}

.ai-copytoolkit-recent .recent-list {
    max-height: 400px;
    overflow-y: auto;
}

.recent-item {
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.recent-item:last-child {
    border-bottom: none;
}

.recent-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.recent-date {
    font-size: 12px;
    color: #666;
}

.recent-content {
    color: #555;
    margin-bottom: 8px;
    line-height: 1.4;
}

.recent-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
}

.no-content {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.getting-started-steps {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.step {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    border-radius: 4px;
    background: #f8f9fa;
}

.step.current {
    background: #e7f3ff;
    border-left: 4px solid #0073aa;
}

.step.completed {
    background: #e8f5e8;
    border-left: 4px solid #46b450;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #ccc;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.step.current .step-number {
    background: #0073aa;
}

.step.completed .step-number {
    background: #46b450;
}

.step-content h3 {
    margin: 0 0 5px 0;
    font-size: 14px;
}

.step-content p {
    margin: 0 0 10px 0;
    font-size: 13px;
    color: #666;
}

.tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tips-list li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.tips-list li:last-child {
    border-bottom: none;
}

.status-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.status-value {
    font-weight: 600;
}

.status-good {
    color: #46b450;
}

.status-warning {
    color: #ffb900;
}

.status-error {
    color: #dc3232;
}

/* ==========================================================================
   Content Generation Styles
   ========================================================================== */

.ai-copytoolkit-generate-container {
    max-width: 1200px;
}

.template-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #ccd0d4;
}

.template-tab {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    font-weight: 500;
}

.template-tab.active {
    border-bottom-color: #0073aa;
    color: #0073aa;
}

.template-categories {
    margin-top: 20px;
}

.template-category {
    margin-bottom: 30px;
}

.template-category h3 {
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.template-card {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.template-card:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
}

.template-card.selected {
    border-color: #0073aa;
    background: #e7f3ff;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.template-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.template-badge {
    background: #0073aa;
    color: #fff;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    text-transform: uppercase;
}

.template-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 1.4;
}

.template-meta {
    font-size: 12px;
    color: #999;
}

.custom-prompt-section {
    margin-top: 20px;
}

.custom-prompt-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.custom-prompt-section textarea {
    width: 100%;
    resize: vertical;
}

.template-parameters-form {
    margin-top: 15px;
}

.parameter-group {
    margin-bottom: 15px;
}

.parameter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #23282d;
}

.parameter-group input,
.parameter-group textarea,
.parameter-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.parameter-group textarea {
    resize: vertical;
    min-height: 80px;
}

.parameter-group .required {
    color: #dc3232;
    margin-left: 3px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-group label {
    font-weight: 600;
    color: #23282d;
}

.setting-group input[type="range"] {
    width: 100%;
}

.temperature-value {
    font-weight: bold;
    color: #0073aa;
}

.generate-action {
    text-align: center;
}

.generate-action button {
    padding: 12px 24px;
    font-size: 16px;
}

.generation-status {
    margin-top: 15px;
    font-style: italic;
    color: #666;
}

.generated-content {
    margin-top: 20px;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.content-actions {
    display: flex;
    gap: 10px;
}

.content-body textarea {
    width: 100%;
    resize: vertical;
    font-family: monospace;
    background: #f8f9fa;
    border: 1px solid #ddd;
}

.content-meta {
    display: flex;
    gap: 20px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    font-size: 14px;
}

.meta-item strong {
    color: #23282d;
}

/* ==========================================================================
   Templates Management Styles
   ========================================================================== */

.ai-copytoolkit-templates-container {
    max-width: 1200px;
}

.template-filters {
    display: flex;
    gap: 20px;
    align-items: flex-end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 200px;
}

.filter-group label {
    font-weight: 600;
    font-size: 13px;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.template-item {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    background: #fff;
}

.template-item .template-header {
    margin-bottom: 15px;
}

.template-badges {
    display: flex;
    gap: 5px;
}

.badge {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 600;
}

.badge-default {
    background: #0073aa;
    color: #fff;
}

.badge-type {
    background: #f0f0f1;
    color: #50575e;
}

.template-preview {
    margin: 15px 0;
}

.prompt-preview {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.template-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
    margin: 15px 0;
}

.template-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* ==========================================================================
   Modal Styles
   ========================================================================== */

.ai-copytoolkit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 4px;
    max-width: 600px;
    width: 90%;
    max-height: 90%;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-large {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #eee;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

/* ==========================================================================
   History Styles
   ========================================================================== */

.history-filters {
    display: flex;
    gap: 20px;
    align-items: flex-end;
    flex-wrap: wrap;
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-left: auto;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.history-stats {
    font-size: 14px;
    color: #666;
}

.history-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.history-item {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background: #fff;
}

.history-item .history-header {
    margin-bottom: 10px;
}

.history-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-status {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 600;
}

.status-completed {
    background: #e8f5e8;
    color: #46b450;
}

.status-failed {
    background: #ffeaea;
    color: #dc3232;
}

.history-content {
    color: #555;
    margin-bottom: 10px;
    line-height: 1.4;
}

.history-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.history-actions {
    display: flex;
    gap: 8px;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
    color: #666;
}

.no-history {
    text-align: center;
    padding: 40px;
    color: #666;
}

/* ==========================================================================
   Settings Styles
   ========================================================================== */

.ai-copytoolkit-settings-container {
    max-width: 800px;
}

.usage-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.usage-stats .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
}

.usage-stats .stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.usage-stats .stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #0073aa;
}

.temperature-display {
    font-weight: bold;
    color: #0073aa;
    margin-left: 10px;
}

/* ==========================================================================
   License Management Styles
   ========================================================================== */

.license-status {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 20px;
    align-items: start;
    margin-bottom: 20px;
}

.license-info h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.status-display {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.status-display .dashicons {
    font-size: 20px;
}

.status-valid {
    background: #e8f5e8;
    color: #46b450;
    border: 1px solid #46b450;
}

.status-expired,
.status-invalid,
.status-suspended {
    background: #ffeaea;
    color: #dc3232;
    border: 1px solid #dc3232;
}

.status-inactive {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffc107;
}

.license-details {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #0073aa;
}

.license-details p {
    margin: 5px 0;
}

.license-activation {
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 300px;
}

.license-activation label {
    font-weight: 600;
}

.license-activation input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.license-management {
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 200px;
}

.license-help {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
    margin-top: 20px;
}

.license-help h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.license-help ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.license-help li {
    margin-bottom: 8px;
}

.license-help a {
    text-decoration: none;
    color: #0073aa;
}

.license-help a:hover {
    text-decoration: underline;
}

#license-result {
    margin-top: 15px;
}

@media (max-width: 768px) {
    .license-status {
        grid-template-columns: 1fr;
    }

    .license-activation,
    .license-management {
        min-width: auto;
    }
}

/* ==========================================================================
   Enhanced Generation Settings Styles
   ========================================================================== */

.generation-settings .settings-grid {
    display: grid;
    gap: 20px;
    margin-bottom: 20px;
}

.model-select-wrapper {
    display: flex;
    gap: 10px;
    align-items: center;
}

.model-select-wrapper select {
    flex: 1;
    max-height: 400px;
    overflow-y: auto;
}

/* Style for model categories in dropdown */
.model-select-wrapper select option[disabled] {
    font-weight: bold;
    background-color: #f0f0f0 !important;
    color: #666 !important;
    font-style: italic;
}

/* Free model styling */
.model-select-wrapper select option[value*="🆓"] {
    color: #28a745;
    font-weight: 500;
}

/* Premium model styling */
.model-select-wrapper select option:not([disabled]):not([value*="🆓"]) {
    padding-left: 20px;
}

.model-select-wrapper .button {
    padding: 6px 12px;
    height: auto;
    line-height: 1.2;
}

.model-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

.model-details {
    display: flex;
    gap: 15px;
    font-size: 13px;
}

.model-details span {
    color: #666;
}

.model-name {
    font-weight: 600;
    color: #333 !important;
}

.model-capabilities {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.capability-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
}

.capability-tag:nth-child(odd) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.capability-tag:nth-child(3n) {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.token-input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.token-presets {
    display: flex;
    gap: 8px;
}

.token-preset {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.token-preset:hover {
    background: #e9ecef;
    border-color: #0073aa;
}

.token-preset.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.temperature-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.temperature-control input[type="range"] {
    flex: 1;
}

.temperature-value {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-family: monospace;
    font-size: 14px;
    min-width: 40px;
    text-align: center;
}

.temperature-presets {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
}

.temp-preset {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.temp-preset:hover {
    background: #e9ecef;
    border-color: #0073aa;
}

.temp-preset.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.quick-settings {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-setting {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-setting:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.quick-setting[data-preset="factual"] {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.quick-setting[data-preset="marketing"] {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.quick-setting[data-preset="creative"] {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.settings-actions {
    display: flex;
    gap: 10px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
}

#token-estimate {
    color: #666;
    font-style: italic;
}

#temperature-description {
    color: #666;
    font-style: italic;
}

/* Spinning animation for refresh button */
.dashicons.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .model-select-wrapper {
        flex-direction: column;
        align-items: stretch;
    }

    .token-presets,
    .temperature-presets,
    .quick-settings {
        flex-wrap: wrap;
    }

    .settings-actions {
        flex-direction: column;
    }

    .model-details {
        flex-direction: column;
        gap: 5px;
    }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .ai-copytoolkit-dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .template-grid {
        grid-template-columns: 1fr;
    }
    
    .templates-grid {
        grid-template-columns: 1fr;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .template-filters,
    .history-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-actions {
        margin-left: 0;
        margin-top: 10px;
    }
    
    .content-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .content-actions {
        justify-content: center;
    }
    
    .content-meta {
        flex-direction: column;
        gap: 10px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-0 {
    margin-bottom: 0;
}

.mt-20 {
    margin-top: 20px;
}

.hidden {
    display: none;
}

.spinner.is-active {
    visibility: visible;
}

.copy-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.copy-button:hover {
    background: #005a87;
}

.prompt-content,
.content-area {
    position: relative;
}
