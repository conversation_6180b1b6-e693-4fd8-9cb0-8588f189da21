# AI CopyToolkit – Smart Copywriting Assistant
# Copyright (C) 2024 AI CopyToolkit
# This file is distributed under the same license as the AI CopyToolkit package.
msgid ""
msgstr ""
"Project-Id-Version: AI CopyToolkit 1.0.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"

#: ai-copytoolkit.php:25
msgid "Generate high-converting marketing content using AI models from OpenRouter. Create product descriptions, ad copy, email subject lines, CTAs, and social media posts with ease."
msgstr ""

#: includes/class-admin.php:65
msgid "AI CopyToolkit"
msgstr ""

#: includes/class-admin.php:66
msgid "Dashboard"
msgstr ""

#: includes/class-admin.php:75
msgid "Generate Content"
msgstr ""

#: includes/class-admin.php:84
msgid "Templates"
msgstr ""

#: includes/class-admin.php:93
msgid "History"
msgstr ""

#: includes/class-admin.php:102
msgid "Settings"
msgstr ""

#: includes/class-admin.php:111
msgid "System Logs"
msgstr ""

#: includes/class-admin.php:200
msgid "Insufficient permissions"
msgstr ""

#: includes/class-admin.php:250
msgid "Settings saved successfully"
msgstr ""

#: includes/class-admin.php:252
msgid "Failed to save settings"
msgstr ""

#: includes/class-admin.php:280
msgid "History entry deleted"
msgstr ""

#: includes/class-admin.php:282
msgid "Failed to delete history entry"
msgstr ""

#: includes/class-admin.php:300
msgid "No history to export"
msgstr ""

#: includes/class-admin.php:320
msgid "Cleared %d history entries"
msgstr ""

#: includes/class-admin.php:322
msgid "Failed to clear history"
msgstr ""

#: includes/class-admin.php:340
msgid "History item not found"
msgstr ""

#: includes/class-api.php:70
msgid "API connection successful"
msgstr ""

#: includes/class-api.php:90
msgid "OpenRouter API key not configured"
msgstr ""

#: includes/class-api.php:150
msgid "Rate limit exceeded. Please try again later."
msgstr ""

#: includes/class-api.php:180
msgid "No response received from AI model"
msgstr ""

#: includes/class-api.php:220
msgid "API key is required"
msgstr ""

#: includes/class-api.php:250
msgid "Invalid response from API"
msgstr ""

#: includes/class-content-generator.php:80
msgid "Template or custom prompt is required"
msgstr ""

#: includes/class-content-generator.php:120
msgid "Template not found"
msgstr ""

#: includes/class-content-generator.php:140
msgid "Unable to generate prompt"
msgstr ""

#: includes/class-content-generator.php:200
msgid "Failed to create template"
msgstr ""

#: includes/class-content-generator.php:220
msgid "Failed to update template"
msgstr ""

#: includes/class-content-generator.php:240
msgid "Cannot delete default templates"
msgstr ""

#: includes/class-content-generator.php:250
msgid "You can only delete your own templates"
msgstr ""

#: includes/class-content-generator.php:260
msgid "Failed to delete template"
msgstr ""

#: includes/class-database.php:200
msgid "E-commerce"
msgstr ""

#: includes/class-database.php:201
msgid "Advertising"
msgstr ""

#: includes/class-database.php:202
msgid "Email Marketing"
msgstr ""

#: includes/class-database.php:203
msgid "Social Media"
msgstr ""

#: includes/class-database.php:204
msgid "Conversion"
msgstr ""

#: includes/class-database.php:205
msgid "Content Marketing"
msgstr ""

#: includes/class-database.php:206
msgid "Custom"
msgstr ""

#: includes/class-security.php:100
msgid "Invalid email address"
msgstr ""

#: includes/class-security.php:105
msgid "Invalid URL"
msgstr ""

#: includes/class-security.php:110
msgid "Invalid number"
msgstr ""

#: includes/class-security.php:115
msgid "Value must be at least %d"
msgstr ""

#: includes/class-security.php:120
msgid "Value must be no more than %d"
msgstr ""

#: includes/class-security.php:130
msgid "Invalid decimal number"
msgstr ""

#: includes/class-security.php:140
msgid "String must be at least %d characters"
msgstr ""

#: includes/class-security.php:145
msgid "String must be no more than %d characters"
msgstr ""

#: includes/class-security.php:150
msgid "String format is invalid"
msgstr ""

#: includes/class-security.php:160
msgid "This field is required"
msgstr ""

#: includes/class-security.php:165
msgid "API key is required"
msgstr ""

#: includes/class-security.php:170
msgid "API key appears to be invalid"
msgstr ""

#: admin/pages/dashboard.php:30
msgid "AI CopyToolkit Dashboard"
msgstr ""

#: admin/pages/dashboard.php:35
msgid "Welcome to AI CopyToolkit!"
msgstr ""

#: admin/pages/dashboard.php:40
msgid "To get started, please <a href=\"%s\">configure your OpenRouter API key</a> in the settings."
msgstr ""

#: admin/pages/dashboard.php:50
msgid "Quick Stats"
msgstr ""

#: admin/pages/dashboard.php:55
msgid "Content Generated"
msgstr ""

#: admin/pages/dashboard.php:60
msgid "Tokens Used"
msgstr ""

#: admin/pages/dashboard.php:65
msgid "Templates Available"
msgstr ""

#: admin/pages/dashboard.php:75
msgid "Quick Actions"
msgstr ""

#: admin/pages/dashboard.php:85
msgid "Browse Templates"
msgstr ""

#: admin/pages/dashboard.php:90
msgid "View History"
msgstr ""

#: admin/pages/dashboard.php:100
msgid "Recent Generations"
msgstr ""

#: admin/pages/dashboard.php:110
msgid "Custom Prompt"
msgstr ""

#: admin/pages/dashboard.php:120
msgid "ago"
msgstr ""

#: admin/pages/dashboard.php:130
msgid "tokens"
msgstr ""

#: admin/pages/dashboard.php:140
msgid "View All History"
msgstr ""

#: admin/pages/dashboard.php:150
msgid "No content generated yet."
msgstr ""

#: admin/pages/dashboard.php:155
msgid "Generate Your First Content"
msgstr ""

#: admin/pages/dashboard.php:165
msgid "Getting Started"
msgstr ""

#: admin/pages/dashboard.php:170
msgid "Configure API Key"
msgstr ""

#: admin/pages/dashboard.php:175
msgid "Set up your OpenRouter API key to access AI models."
msgstr ""

#: admin/pages/dashboard.php:180
msgid "Configure Now"
msgstr ""

#: admin/pages/dashboard.php:190
msgid "Choose a template and generate your first piece of content."
msgstr ""

#: admin/pages/dashboard.php:195
msgid "Start Generating"
msgstr ""

#: admin/pages/dashboard.php:205
msgid "Explore Features"
msgstr ""

#: admin/pages/dashboard.php:210
msgid "Try different templates, create custom ones, and explore the history."
msgstr ""

#: admin/pages/dashboard.php:220
msgid "Tips & Tricks"
msgstr ""

#: admin/pages/dashboard.php:225
msgid "Be Specific:"
msgstr ""

#: admin/pages/dashboard.php:230
msgid "The more specific your input, the better the AI-generated content will be."
msgstr ""

#: admin/pages/dashboard.php:235
msgid "Try Different Models:"
msgstr ""

#: admin/pages/dashboard.php:240
msgid "Different AI models excel at different types of content. Experiment to find the best fit."
msgstr ""

#: admin/pages/dashboard.php:245
msgid "Adjust Temperature:"
msgstr ""

#: admin/pages/dashboard.php:250
msgid "Lower values (0.3-0.5) for factual content, higher values (0.7-0.9) for creative content."
msgstr ""

#: admin/pages/dashboard.php:255
msgid "Save Custom Templates:"
msgstr ""

#: admin/pages/dashboard.php:260
msgid "Create and save templates for content types you generate frequently."
msgstr ""

#: admin/pages/dashboard.php:270
msgid "System Status"
msgstr ""

#: admin/pages/dashboard.php:275
msgid "API Connection:"
msgstr ""

#: admin/pages/dashboard.php:280
msgid "Connected"
msgstr ""

#: admin/pages/dashboard.php:285
msgid "Not Configured"
msgstr ""

#: admin/pages/dashboard.php:290
msgid "Plugin Version:"
msgstr ""

#: admin/pages/dashboard.php:295
msgid "WordPress Version:"
msgstr ""

#: admin/pages/dashboard.php:300
msgid "PHP Version:"
msgstr ""

#: admin/pages/generate.php:30
msgid "API Key Required"
msgstr ""

#: admin/pages/generate.php:35
msgid "Please <a href=\"%s\">configure your OpenRouter API key</a> before generating content."
msgstr ""

#: admin/pages/generate.php:45
msgid "Choose a Template"
msgstr ""

#: admin/pages/generate.php:50
msgid "Custom Prompt"
msgstr ""

#: admin/pages/generate.php:70
msgid "Default"
msgstr ""

#: admin/pages/generate.php:90
msgid "No templates available."
msgstr ""

#: admin/pages/generate.php:100
msgid "Enter your custom prompt:"
msgstr ""

#: admin/pages/generate.php:105
msgid "Describe what kind of content you want to generate..."
msgstr ""

#: admin/pages/generate.php:110
msgid "Be as specific as possible for better results. Include details about tone, length, target audience, and any specific requirements."
msgstr ""

#: admin/pages/generate.php:120
msgid "Template Parameters"
msgstr ""

#: admin/pages/generate.php:130
msgid "Generation Settings"
msgstr ""

#: admin/pages/generate.php:135
msgid "AI Model:"
msgstr ""

#: admin/pages/generate.php:140
msgid "Loading models..."
msgstr ""

#: admin/pages/generate.php:145
msgid "Different models excel at different types of content."
msgstr ""

#: admin/pages/generate.php:150
msgid "Max Tokens:"
msgstr ""

#: admin/pages/generate.php:155
msgid "Maximum length of generated content (roughly 4 characters per token)."
msgstr ""

#: admin/pages/generate.php:160
msgid "Temperature:"
msgstr ""

#: admin/pages/generate.php:165
msgid "Controls creativity: Lower = more focused, Higher = more creative."
msgstr ""

#: admin/pages/generate.php:180
msgid "Generated Content"
msgstr ""

#: admin/pages/generate.php:185
msgid "Copy"
msgstr ""

#: admin/pages/generate.php:190
msgid "Regenerate"
msgstr ""

#: admin/pages/generate.php:195
msgid "Save to History"
msgstr ""

#: admin/pages/generate.php:205
msgid "Model Used:"
msgstr ""

#: admin/pages/generate.php:210
msgid "Tokens Used:"
msgstr ""

#: admin/pages/generate.php:215
msgid "Generation Time:"
msgstr ""
