<?php
/**
 * Settings page template
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current settings
$settings = AI_CopyToolkit::get_settings();

// Get license information
$license = AI_CopyToolkit_License::instance();
$license_info = $license->get_license_info();
?>

<div class="wrap ai-copytoolkit-settings">
    <h1 class="wp-heading-inline">
        <?php _e('AI CopyToolkit Settings', 'ai-copytoolkit'); ?>
    </h1>
    
    <div class="ai-copytoolkit-settings-container">
        <form id="settings-form" method="post" action="options.php">
            <?php settings_fields('ai_copytoolkit_settings'); ?>

            <!-- License Configuration -->
            <div class="ai-copytoolkit-card" id="license">
                <h2><?php _e('License Configuration', 'ai-copytoolkit'); ?></h2>

                <div class="license-status">
                    <div class="license-info">
                        <h3><?php _e('License Status', 'ai-copytoolkit'); ?></h3>
                        <div class="status-display status-<?php echo esc_attr($license_info['status']); ?>">
                            <?php
                            switch ($license_info['status']) {
                                case 'valid':
                                    echo '<span class="dashicons dashicons-yes-alt"></span> ' . __('Active', 'ai-copytoolkit');
                                    break;
                                case 'expired':
                                    echo '<span class="dashicons dashicons-warning"></span> ' . __('Expired', 'ai-copytoolkit');
                                    break;
                                case 'invalid':
                                    echo '<span class="dashicons dashicons-dismiss"></span> ' . __('Invalid', 'ai-copytoolkit');
                                    break;
                                case 'suspended':
                                    echo '<span class="dashicons dashicons-warning"></span> ' . __('Suspended', 'ai-copytoolkit');
                                    break;
                                default:
                                    echo '<span class="dashicons dashicons-minus"></span> ' . __('Inactive', 'ai-copytoolkit');
                            }
                            ?>
                        </div>

                        <?php if ($license_info['status'] === 'valid'): ?>
                            <div class="license-details">
                                <?php if ($license_info['expires'] && $license_info['expires'] !== 'lifetime'): ?>
                                    <p><strong><?php _e('Expires:', 'ai-copytoolkit'); ?></strong> <?php echo date_i18n(get_option('date_format'), strtotime($license_info['expires'])); ?></p>
                                <?php endif; ?>

                                <?php if ($license_info['customer_name']): ?>
                                    <p><strong><?php _e('Licensed to:', 'ai-copytoolkit'); ?></strong> <?php echo esc_html($license_info['customer_name']); ?></p>
                                <?php endif; ?>

                                <?php if ($license_info['activations_left'] !== null): ?>
                                    <p><strong><?php _e('Activations remaining:', 'ai-copytoolkit'); ?></strong> <?php echo intval($license_info['activations_left']); ?></p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="license-actions">
                        <?php if ($license_info['status'] !== 'valid'): ?>
                            <div class="license-activation">
                                <label for="license-key"><?php _e('License Key:', 'ai-copytoolkit'); ?></label>
                                <input type="text" id="license-key" value="<?php echo esc_attr($license_info['key']); ?>" placeholder="<?php _e('Enter your license key', 'ai-copytoolkit'); ?>">
                                <button type="button" id="activate-license" class="button button-primary">
                                    <?php _e('Activate License', 'ai-copytoolkit'); ?>
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="license-management">
                                <button type="button" id="check-license" class="button button-secondary">
                                    <?php _e('Check License', 'ai-copytoolkit'); ?>
                                </button>
                                <button type="button" id="deactivate-license" class="button button-secondary">
                                    <?php _e('Deactivate License', 'ai-copytoolkit'); ?>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div id="license-result"></div>

                <div class="license-help">
                    <h4><?php _e('Need Help?', 'ai-copytoolkit'); ?></h4>
                    <ul>
                        <li><a href="#" target="_blank"><?php _e('Purchase a License', 'ai-copytoolkit'); ?></a></li>
                        <li><a href="#" target="_blank"><?php _e('Manage Your Licenses', 'ai-copytoolkit'); ?></a></li>
                        <li><a href="#" target="_blank"><?php _e('Contact Support', 'ai-copytoolkit'); ?></a></li>
                    </ul>
                </div>
            </div>

            <!-- API Configuration -->
            <div class="ai-copytoolkit-card">
                <h2><?php _e('API Configuration', 'ai-copytoolkit'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="api_key"><?php _e('OpenRouter API Key', 'ai-copytoolkit'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="api_key" name="ai_copytoolkit_settings[api_key]" 
                                   value="<?php echo esc_attr($settings['api_key'] ?? ''); ?>" 
                                   class="regular-text" autocomplete="off">
                            <button type="button" id="toggle-api-key" class="button button-secondary">
                                <?php _e('Show', 'ai-copytoolkit'); ?>
                            </button>
                            <button type="button" id="test-api-key" class="button button-secondary">
                                <?php _e('Test Connection', 'ai-copytoolkit'); ?>
                            </button>
                            <button type="button" id="debug-api" class="button button-secondary">
                                <?php _e('Debug API', 'ai-copytoolkit'); ?>
                            </button>
                            <p class="description">
                                <?php printf(
                                    __('Get your API key from <a href="%s" target="_blank">OpenRouter</a>. Free tier available.', 'ai-copytoolkit'),
                                    'https://openrouter.ai/keys'
                                ); ?>
                            </p>
                            <div id="api-test-result"></div>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Default Generation Settings -->
            <div class="ai-copytoolkit-card">
                <h2><?php _e('Default Generation Settings', 'ai-copytoolkit'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="default_model"><?php _e('Default AI Model', 'ai-copytoolkit'); ?></label>
                        </th>
                        <td>
                            <select id="default_model" name="ai_copytoolkit_settings[default_model]">
                                <option value=""><?php _e('Loading models...', 'ai-copytoolkit'); ?></option>
                            </select>
                            <p class="description">
                                <?php _e('The default AI model to use for content generation.', 'ai-copytoolkit'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="max_tokens"><?php _e('Max Tokens', 'ai-copytoolkit'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="max_tokens" name="ai_copytoolkit_settings[max_tokens]" 
                                   value="<?php echo esc_attr($settings['max_tokens'] ?? 500); ?>" 
                                   min="50" max="4000" step="50" class="small-text">
                            <p class="description">
                                <?php _e('Maximum number of tokens to generate (roughly 4 characters per token).', 'ai-copytoolkit'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="temperature"><?php _e('Temperature', 'ai-copytoolkit'); ?></label>
                        </th>
                        <td>
                            <input type="range" id="temperature" name="ai_copytoolkit_settings[temperature]" 
                                   value="<?php echo esc_attr($settings['temperature'] ?? 0.7); ?>" 
                                   min="0" max="1" step="0.1">
                            <span class="temperature-display"><?php echo esc_attr($settings['temperature'] ?? 0.7); ?></span>
                            <p class="description">
                                <?php _e('Controls creativity: 0.1-0.3 for factual content, 0.7-0.9 for creative content.', 'ai-copytoolkit'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- History Settings -->
            <div class="ai-copytoolkit-card">
                <h2><?php _e('History Settings', 'ai-copytoolkit'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="enable_history"><?php _e('Enable History', 'ai-copytoolkit'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" id="enable_history" name="ai_copytoolkit_settings[enable_history]" 
                                   value="1" <?php checked($settings['enable_history'] ?? true); ?>>
                            <label for="enable_history"><?php _e('Save generation history', 'ai-copytoolkit'); ?></label>
                            <p class="description">
                                <?php _e('When enabled, all generated content will be saved to history.', 'ai-copytoolkit'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="history_limit"><?php _e('History Limit', 'ai-copytoolkit'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="history_limit" name="ai_copytoolkit_settings[history_limit]" 
                                   value="<?php echo esc_attr($settings['history_limit'] ?? 100); ?>" 
                                   min="0" max="1000" step="10" class="small-text">
                            <p class="description">
                                <?php _e('Maximum number of history entries to keep per user (0 = unlimited).', 'ai-copytoolkit'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Advanced Settings -->
            <div class="ai-copytoolkit-card">
                <h2><?php _e('Advanced Settings', 'ai-copytoolkit'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="enable_logging"><?php _e('Enable Logging', 'ai-copytoolkit'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" id="enable_logging" name="ai_copytoolkit_settings[enable_logging]" 
                                   value="1" <?php checked($settings['enable_logging'] ?? true); ?>>
                            <label for="enable_logging"><?php _e('Enable error logging and debugging', 'ai-copytoolkit'); ?></label>
                            <p class="description">
                                <?php _e('When enabled, errors and debug information will be logged to the WordPress error log.', 'ai-copytoolkit'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Usage Statistics -->
            <div class="ai-copytoolkit-card">
                <h2><?php _e('Usage Statistics', 'ai-copytoolkit'); ?></h2>
                
                <div class="usage-stats">
                    <div class="stat-item">
                        <div class="stat-label"><?php _e('Total Generations:', 'ai-copytoolkit'); ?></div>
                        <div class="stat-value" id="total-generations">-</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label"><?php _e('Total Tokens Used:', 'ai-copytoolkit'); ?></div>
                        <div class="stat-value" id="total-tokens">-</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label"><?php _e('This Month:', 'ai-copytoolkit'); ?></div>
                        <div class="stat-value" id="month-generations">-</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label"><?php _e('Most Used Model:', 'ai-copytoolkit'); ?></div>
                        <div class="stat-value" id="popular-model">-</div>
                    </div>
                </div>
                
                <button type="button" id="refresh-stats" class="button button-secondary">
                    <?php _e('Refresh Statistics', 'ai-copytoolkit'); ?>
                </button>
            </div>
            
            <!-- System Information -->
            <div class="ai-copytoolkit-card">
                <h2><?php _e('System Information', 'ai-copytoolkit'); ?></h2>
                
                <table class="widefat">
                    <tbody>
                        <tr>
                            <td><strong><?php _e('Plugin Version:', 'ai-copytoolkit'); ?></strong></td>
                            <td><?php echo AI_COPYTOOLKIT_VERSION; ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('WordPress Version:', 'ai-copytoolkit'); ?></strong></td>
                            <td><?php echo get_bloginfo('version'); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('PHP Version:', 'ai-copytoolkit'); ?></strong></td>
                            <td><?php echo PHP_VERSION; ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('Database Version:', 'ai-copytoolkit'); ?></strong></td>
                            <td><?php echo get_option('ai_copytoolkit_db_version', '1.0.0'); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('cURL Support:', 'ai-copytoolkit'); ?></strong></td>
                            <td><?php echo function_exists('curl_version') ? __('Yes', 'ai-copytoolkit') : __('No', 'ai-copytoolkit'); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('OpenSSL Support:', 'ai-copytoolkit'); ?></strong></td>
                            <td><?php echo extension_loaded('openssl') ? __('Yes', 'ai-copytoolkit') : __('No', 'ai-copytoolkit'); ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <?php submit_button(__('Save Settings', 'ai-copytoolkit'), 'primary', 'submit', true, array('id' => 'save-settings')); ?>
        </form>
        
        <!-- Reset Settings -->
        <div class="ai-copytoolkit-card">
            <h2><?php _e('Reset Settings', 'ai-copytoolkit'); ?></h2>
            <p><?php _e('Reset all settings to their default values. This action cannot be undone.', 'ai-copytoolkit'); ?></p>
            <button type="button" id="reset-settings" class="button button-secondary">
                <?php _e('Reset to Defaults', 'ai-copytoolkit'); ?>
            </button>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Load available models
    loadAvailableModels();
    
    // Load usage statistics
    loadUsageStats();
    
    // Toggle API key visibility
    $('#toggle-api-key').on('click', function() {
        const $input = $('#api_key');
        const $button = $(this);
        
        if ($input.attr('type') === 'password') {
            $input.attr('type', 'text');
            $button.text('<?php _e('Hide', 'ai-copytoolkit'); ?>');
        } else {
            $input.attr('type', 'password');
            $button.text('<?php _e('Show', 'ai-copytoolkit'); ?>');
        }
    });
    
    // Test API connection
    $('#test-api-key').on('click', function() {
        const apiKey = $('#api_key').val();
        const $button = $(this);
        const $result = $('#api-test-result');
        
        if (!apiKey) {
            $result.html('<div class="notice notice-error inline"><p><?php _e('Please enter an API key first.', 'ai-copytoolkit'); ?></p></div>');
            return;
        }
        
        $button.prop('disabled', true).text('<?php _e('Testing...', 'ai-copytoolkit'); ?>');
        $result.html('<div class="notice notice-info inline"><p><?php _e('Testing API connection...', 'ai-copytoolkit'); ?></p></div>');
        
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_test_api',
            nonce: aiCopyToolkit.nonce,
            api_key: apiKey
        }, function(response) {
            if (response.success) {
                $result.html('<div class="notice notice-success inline"><p>' + response.data + '</p></div>');
                loadAvailableModels(); // Reload models with new API key
            } else {
                $result.html('<div class="notice notice-error inline"><p>' + response.data + '</p></div>');
            }
        }).fail(function() {
            $result.html('<div class="notice notice-error inline"><p><?php _e('Connection test failed.', 'ai-copytoolkit'); ?></p></div>');
        }).always(function() {
            $button.prop('disabled', false).text('<?php _e('Test Connection', 'ai-copytoolkit'); ?>');
        });
    });

    // Debug API connection
    $('#debug-api').on('click', function() {
        const $button = $(this);
        const $result = $('#api-test-result');

        $button.prop('disabled', true).text('<?php _e('Debugging...', 'ai-copytoolkit'); ?>');
        $result.html('<div class="notice notice-info inline"><p><?php _e('Running API diagnostics...', 'ai-copytoolkit'); ?></p></div>');

        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_debug_api',
            nonce: aiCopyToolkit.nonce
        }, function(response) {
            if (response.success) {
                const debug = response.data;
                let debugHtml = '<div class="notice notice-info inline"><h4><?php _e('API Debug Information:', 'ai-copytoolkit'); ?></h4>';
                debugHtml += '<ul style="margin-left: 20px;">';
                debugHtml += '<li><strong><?php _e('API Key:', 'ai-copytoolkit'); ?></strong> ' + (debug.api_key_configured ? '✅ Configured (' + debug.api_key_length + ' chars)' : '❌ Not configured') + '</li>';
                debugHtml += '<li><strong><?php _e('Basic Connectivity:', 'ai-copytoolkit'); ?></strong> ' + (debug.basic_connectivity ? '✅ Working' : '❌ Failed') + '</li>';
                debugHtml += '<li><strong><?php _e('OpenRouter API:', 'ai-copytoolkit'); ?></strong> ' + (debug.openrouter_api_test ? '✅ Working (' + (debug.openrouter_models_count || 0) + ' models)' : '❌ Failed') + '</li>';
                debugHtml += '<li><strong><?php _e('cURL:', 'ai-copytoolkit'); ?></strong> ' + (debug.curl_available ? '✅ Available' : '❌ Not available') + '</li>';
                debugHtml += '<li><strong><?php _e('OpenSSL:', 'ai-copytoolkit'); ?></strong> ' + (debug.openssl_available ? '✅ Available' : '❌ Not available') + '</li>';
                debugHtml += '<li><strong><?php _e('WordPress Version:', 'ai-copytoolkit'); ?></strong> ' + debug.wordpress_version + '</li>';
                debugHtml += '<li><strong><?php _e('PHP Version:', 'ai-copytoolkit'); ?></strong> ' + debug.php_version + '</li>';
                if (debug.openrouter_error) {
                    debugHtml += '<li><strong><?php _e('OpenRouter Error:', 'ai-copytoolkit'); ?></strong> ' + debug.openrouter_error + '</li>';
                }
                if (debug.connectivity_error) {
                    debugHtml += '<li><strong><?php _e('Connectivity Error:', 'ai-copytoolkit'); ?></strong> ' + debug.connectivity_error + '</li>';
                }
                debugHtml += '</ul></div>';
                $result.html(debugHtml);
            } else {
                $result.html('<div class="notice notice-error inline"><p>' + response.data + '</p></div>');
            }
        }).fail(function() {
            $result.html('<div class="notice notice-error inline"><p><?php _e('Debug test failed.', 'ai-copytoolkit'); ?></p></div>');
        }).always(function() {
            $button.prop('disabled', false).text('<?php _e('Debug API', 'ai-copytoolkit'); ?>');
        });
    });

    // Temperature slider
    $('#temperature').on('input', function() {
        $('.temperature-display').text($(this).val());
    });
    
    // Save settings via AJAX
    $('#settings-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $button = $('#save-settings');
        
        $button.prop('disabled', true);
        
        const formData = $form.serializeArray();
        const settings = {};
        
        formData.forEach(function(item) {
            if (item.name.startsWith('ai_copytoolkit_settings[')) {
                const key = item.name.replace('ai_copytoolkit_settings[', '').replace(']', '');
                settings[key] = item.value;
            }
        });
        
        // Handle checkboxes
        settings.enable_history = $('#enable_history').is(':checked');
        settings.enable_logging = $('#enable_logging').is(':checked');
        
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_save_settings',
            nonce: aiCopyToolkit.nonce,
            settings: settings
        }, function(response) {
            if (response.success) {
                showNotice(response.data, 'success');
            } else {
                showNotice(response.data || '<?php _e('Failed to save settings', 'ai-copytoolkit'); ?>', 'error');
            }
        }).fail(function() {
            showNotice('<?php _e('Failed to save settings', 'ai-copytoolkit'); ?>', 'error');
        }).always(function() {
            $button.prop('disabled', false);
        });
    });
    
    // Reset settings
    $('#reset-settings').on('click', function() {
        if (confirm('<?php _e('Are you sure you want to reset all settings to their default values?', 'ai-copytoolkit'); ?>')) {
            // Implementation for resetting settings
            showNotice('<?php _e('Reset functionality would be implemented here', 'ai-copytoolkit'); ?>', 'info');
        }
    });
    
    // Refresh statistics
    $('#refresh-stats').on('click', function() {
        loadUsageStats();
    });

    // License management
    $('#activate-license').on('click', function() {
        activateLicense();
    });

    $('#deactivate-license').on('click', function() {
        if (confirm('<?php _e('Are you sure you want to deactivate your license?', 'ai-copytoolkit'); ?>')) {
            deactivateLicense();
        }
    });

    $('#check-license').on('click', function() {
        checkLicense();
    });
    
    function loadAvailableModels() {
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_get_models',
            nonce: aiCopyToolkit.nonce
        }, function(response) {
            if (response.success) {
                const $select = $('#default_model');
                const currentValue = $select.data('current') || '<?php echo esc_js($settings['default_model'] ?? 'openai/gpt-3.5-turbo'); ?>';
                
                $select.empty();
                
                response.data.forEach(function(model) {
                    const $option = $('<option>', {
                        value: model.id,
                        text: model.name || model.id
                    });
                    
                    if (model.id === currentValue) {
                        $option.prop('selected', true);
                    }
                    
                    $select.append($option);
                });
            } else {
                $('#default_model').html('<option value=""><?php _e('Failed to load models', 'ai-copytoolkit'); ?></option>');
            }
        });
    }
    
    function loadUsageStats() {
        // This would load actual usage statistics
        // For now, we'll show placeholder values
        $('#total-generations').text('0');
        $('#total-tokens').text('0');
        $('#month-generations').text('0');
        $('#popular-model').text('N/A');
    }
    
    function activateLicense() {
        const licenseKey = $('#license-key').val().trim();
        const $button = $('#activate-license');
        const $result = $('#license-result');

        if (!licenseKey) {
            $result.html('<div class="notice notice-error inline"><p><?php _e('Please enter a license key.', 'ai-copytoolkit'); ?></p></div>');
            return;
        }

        $button.prop('disabled', true).text('<?php _e('Activating...', 'ai-copytoolkit'); ?>');
        $result.html('<div class="notice notice-info inline"><p><?php _e('Activating license...', 'ai-copytoolkit'); ?></p></div>');

        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_activate_license',
            nonce: aiCopyToolkit.nonce,
            license_key: licenseKey
        }, function(response) {
            if (response.success) {
                $result.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else {
                $result.html('<div class="notice notice-error inline"><p>' + response.data + '</p></div>');
            }
        }).fail(function() {
            $result.html('<div class="notice notice-error inline"><p><?php _e('Failed to activate license.', 'ai-copytoolkit'); ?></p></div>');
        }).always(function() {
            $button.prop('disabled', false).text('<?php _e('Activate License', 'ai-copytoolkit'); ?>');
        });
    }

    function deactivateLicense() {
        const $button = $('#deactivate-license');
        const $result = $('#license-result');

        $button.prop('disabled', true).text('<?php _e('Deactivating...', 'ai-copytoolkit'); ?>');
        $result.html('<div class="notice notice-info inline"><p><?php _e('Deactivating license...', 'ai-copytoolkit'); ?></p></div>');

        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_deactivate_license',
            nonce: aiCopyToolkit.nonce
        }, function(response) {
            if (response.success) {
                $result.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else {
                $result.html('<div class="notice notice-error inline"><p>' + response.data + '</p></div>');
            }
        }).fail(function() {
            $result.html('<div class="notice notice-error inline"><p><?php _e('Failed to deactivate license.', 'ai-copytoolkit'); ?></p></div>');
        }).always(function() {
            $button.prop('disabled', false).text('<?php _e('Deactivate License', 'ai-copytoolkit'); ?>');
        });
    }

    function checkLicense() {
        const $button = $('#check-license');
        const $result = $('#license-result');

        $button.prop('disabled', true).text('<?php _e('Checking...', 'ai-copytoolkit'); ?>');
        $result.html('<div class="notice notice-info inline"><p><?php _e('Checking license status...', 'ai-copytoolkit'); ?></p></div>');

        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_check_license',
            nonce: aiCopyToolkit.nonce
        }, function(response) {
            if (response.success) {
                $result.html('<div class="notice notice-success inline"><p><?php _e('License status updated.', 'ai-copytoolkit'); ?></p></div>');
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else {
                $result.html('<div class="notice notice-error inline"><p>' + response.data + '</p></div>');
            }
        }).fail(function() {
            $result.html('<div class="notice notice-error inline"><p><?php _e('Failed to check license.', 'ai-copytoolkit'); ?></p></div>');
        }).always(function() {
            $button.prop('disabled', false).text('<?php _e('Check License', 'ai-copytoolkit'); ?>');
        });
    }

    function showNotice(message, type) {
        const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        $('.ai-copytoolkit-settings .wp-heading-inline').after($notice);

        setTimeout(function() {
            $notice.fadeOut();
        }, 3000);
    }
});
</script>
