<?php
/**
 * Templates management page
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get available templates
$content_generator = AI_CopyToolkit_Content_Generator::instance();
$templates = $content_generator->get_templates();

// Group templates by category
$template_categories = array();
foreach ($templates as $template) {
    $template_categories[$template->category][] = $template;
}

$categories = array(
    'ecommerce' => __('E-commerce', 'ai-copytoolkit'),
    'advertising' => __('Advertising', 'ai-copytoolkit'),
    'email_marketing' => __('Email Marketing', 'ai-copytoolkit'),
    'social_media' => __('Social Media', 'ai-copytoolkit'),
    'conversion' => __('Conversion', 'ai-copytoolkit'),
    'content_marketing' => __('Content Marketing', 'ai-copytoolkit'),
    'custom' => __('Custom', 'ai-copytoolkit')
);

$content_types = array(
    'product_description' => __('Product Description', 'ai-copytoolkit'),
    'facebook_ad' => __('Facebook Ad', 'ai-copytoolkit'),
    'google_ad' => __('Google Ad', 'ai-copytoolkit'),
    'email_subject' => __('Email Subject Line', 'ai-copytoolkit'),
    'email_content' => __('Email Content', 'ai-copytoolkit'),
    'social_post' => __('Social Media Post', 'ai-copytoolkit'),
    'cta' => __('Call-to-Action', 'ai-copytoolkit'),
    'blog_post' => __('Blog Post', 'ai-copytoolkit'),
    'landing_page' => __('Landing Page', 'ai-copytoolkit'),
    'custom' => __('Custom', 'ai-copytoolkit')
);
?>

<div class="wrap ai-copytoolkit-templates">
    <h1 class="wp-heading-inline">
        <?php _e('Content Templates', 'ai-copytoolkit'); ?>
    </h1>
    
    <button id="add-template" class="page-title-action">
        <?php _e('Add New Template', 'ai-copytoolkit'); ?>
    </button>
    
    <div class="ai-copytoolkit-templates-container">
        <!-- Template Filters -->
        <div class="ai-copytoolkit-card template-filters">
            <div class="filter-group">
                <label for="category-filter"><?php _e('Filter by Category:', 'ai-copytoolkit'); ?></label>
                <select id="category-filter">
                    <option value=""><?php _e('All Categories', 'ai-copytoolkit'); ?></option>
                    <?php foreach ($categories as $key => $label): ?>
                        <option value="<?php echo esc_attr($key); ?>"><?php echo esc_html($label); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="search-templates"><?php _e('Search Templates:', 'ai-copytoolkit'); ?></label>
                <input type="text" id="search-templates" placeholder="<?php _e('Search by name or description...', 'ai-copytoolkit'); ?>">
            </div>
        </div>
        
        <!-- Templates List -->
        <div class="ai-copytoolkit-card templates-list">
            <?php if (!empty($template_categories)): ?>
                <?php foreach ($template_categories as $category => $category_templates): ?>
                    <div class="template-category-section" data-category="<?php echo esc_attr($category); ?>">
                        <h2 class="category-title"><?php echo esc_html($categories[$category] ?? ucwords(str_replace('_', ' ', $category))); ?></h2>
                        
                        <div class="templates-grid">
                            <?php foreach ($category_templates as $template): ?>
                                <div class="template-item" data-template-id="<?php echo $template->id; ?>" data-category="<?php echo esc_attr($template->category); ?>">
                                    <div class="template-header">
                                        <h3 class="template-name"><?php echo esc_html($template->name); ?></h3>
                                        <div class="template-badges">
                                            <?php if ($template->is_default): ?>
                                                <span class="badge badge-default"><?php _e('Default', 'ai-copytoolkit'); ?></span>
                                            <?php endif; ?>
                                            <span class="badge badge-type"><?php echo esc_html($content_types[$template->content_type] ?? ucwords(str_replace('_', ' ', $template->content_type))); ?></span>
                                        </div>
                                    </div>
                                    
                                    <div class="template-description">
                                        <?php echo esc_html($template->description); ?>
                                    </div>
                                    
                                    <div class="template-preview">
                                        <strong><?php _e('Prompt Preview:', 'ai-copytoolkit'); ?></strong>
                                        <div class="prompt-preview">
                                            <?php echo esc_html(wp_trim_words($template->prompt_template, 20)); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="template-meta">
                                        <span class="created-date">
                                            <?php _e('Created:', 'ai-copytoolkit'); ?>
                                            <?php echo date_i18n(get_option('date_format'), strtotime($template->created_at)); ?>
                                        </span>
                                        <?php if ($template->created_by): ?>
                                            <span class="created-by">
                                                <?php _e('By:', 'ai-copytoolkit'); ?>
                                                <?php echo esc_html(get_userdata($template->created_by)->display_name ?? __('Unknown', 'ai-copytoolkit')); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="template-actions">
                                        <button class="button button-primary use-template" data-template-id="<?php echo $template->id; ?>">
                                            <?php _e('Use Template', 'ai-copytoolkit'); ?>
                                        </button>
                                        <button class="button button-secondary edit-template" data-template-id="<?php echo $template->id; ?>">
                                            <?php _e('Edit', 'ai-copytoolkit'); ?>
                                        </button>
                                        <button class="button button-secondary view-template" data-template-id="<?php echo $template->id; ?>">
                                            <?php _e('View', 'ai-copytoolkit'); ?>
                                        </button>
                                        <?php if (!$template->is_default && ($template->created_by == get_current_user_id() || current_user_can('manage_options'))): ?>
                                            <button class="button button-link-delete delete-template" data-template-id="<?php echo $template->id; ?>">
                                                <?php _e('Delete', 'ai-copytoolkit'); ?>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="no-templates">
                    <h3><?php _e('No Templates Found', 'ai-copytoolkit'); ?></h3>
                    <p><?php _e('Get started by creating your first custom template.', 'ai-copytoolkit'); ?></p>
                    <button id="create-first-template" class="button button-primary">
                        <?php _e('Create Your First Template', 'ai-copytoolkit'); ?>
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Template Modal -->
<div id="template-modal" class="ai-copytoolkit-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modal-title"><?php _e('Template Details', 'ai-copytoolkit'); ?></h2>
            <button class="modal-close">&times;</button>
        </div>
        
        <div class="modal-body">
            <form id="template-form">
                <div class="form-group">
                    <label for="template-name"><?php _e('Template Name:', 'ai-copytoolkit'); ?></label>
                    <input type="text" id="template-name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="template-description"><?php _e('Description:', 'ai-copytoolkit'); ?></label>
                    <textarea id="template-description" name="description" rows="3"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="template-category"><?php _e('Category:', 'ai-copytoolkit'); ?></label>
                        <select id="template-category" name="category" required>
                            <?php foreach ($categories as $key => $label): ?>
                                <option value="<?php echo esc_attr($key); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="template-content-type"><?php _e('Content Type:', 'ai-copytoolkit'); ?></label>
                        <select id="template-content-type" name="content_type" required>
                            <?php foreach ($content_types as $key => $label): ?>
                                <option value="<?php echo esc_attr($key); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="template-prompt"><?php _e('Prompt Template:', 'ai-copytoolkit'); ?></label>
                    <textarea id="template-prompt" name="prompt_template" rows="8" required placeholder="<?php _e('Enter your prompt template here. Use {parameter_name} for dynamic parameters.', 'ai-copytoolkit'); ?>"></textarea>
                    <p class="description">
                        <?php _e('Use curly braces to define parameters, e.g., {product_name}, {target_audience}, {tone}', 'ai-copytoolkit'); ?>
                    </p>
                </div>
                
                <div class="form-group">
                    <label><?php _e('Template Parameters:', 'ai-copytoolkit'); ?></label>
                    <div id="template-parameters">
                        <p class="description"><?php _e('Parameters will be automatically detected from your prompt template.', 'ai-copytoolkit'); ?></p>
                    </div>
                </div>
                
                <input type="hidden" id="template-id" name="id" value="">
            </form>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="button button-secondary" id="modal-cancel"><?php _e('Cancel', 'ai-copytoolkit'); ?></button>
            <button type="button" class="button button-primary" id="modal-save"><?php _e('Save Template', 'ai-copytoolkit'); ?></button>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    let currentTemplateId = null;
    
    // Filter templates
    $('#category-filter').on('change', function() {
        const category = $(this).val();
        if (category) {
            $('.template-category-section').hide();
            $('.template-category-section[data-category="' + category + '"]').show();
        } else {
            $('.template-category-section').show();
        }
    });
    
    // Search templates
    $('#search-templates').on('input', function() {
        const search = $(this).val().toLowerCase();
        $('.template-item').each(function() {
            const name = $(this).find('.template-name').text().toLowerCase();
            const description = $(this).find('.template-description').text().toLowerCase();
            const matches = name.includes(search) || description.includes(search);
            $(this).toggle(matches);
        });
    });
    
    // Add new template
    $('#add-template, #create-first-template').on('click', function() {
        openTemplateModal();
    });
    
    // Edit template
    $('.edit-template').on('click', function() {
        const templateId = $(this).data('template-id');
        openTemplateModal(templateId);
    });
    
    // View template
    $('.view-template').on('click', function() {
        const templateId = $(this).data('template-id');
        openTemplateModal(templateId, true);
    });
    
    // Use template
    $('.use-template').on('click', function() {
        const templateId = $(this).data('template-id');
        window.location.href = '<?php echo admin_url('admin.php?page=ai-copytoolkit-generate'); ?>&template=' + templateId;
    });
    
    // Delete template
    $('.delete-template').on('click', function() {
        if (confirm(aiCopyToolkit.strings.confirm_delete)) {
            const templateId = $(this).data('template-id');
            deleteTemplate(templateId);
        }
    });
    
    // Modal controls
    $('.modal-close, #modal-cancel').on('click', function() {
        closeTemplateModal();
    });
    
    $('#modal-save').on('click', function() {
        saveTemplate();
    });
    
    // Close modal on outside click
    $(window).on('click', function(e) {
        if ($(e.target).is('#template-modal')) {
            closeTemplateModal();
        }
    });
    
    function openTemplateModal(templateId = null, viewOnly = false) {
        currentTemplateId = templateId;
        
        if (templateId) {
            $('#modal-title').text(viewOnly ? '<?php _e('View Template', 'ai-copytoolkit'); ?>' : '<?php _e('Edit Template', 'ai-copytoolkit'); ?>');
            loadTemplateData(templateId);
        } else {
            $('#modal-title').text('<?php _e('Add New Template', 'ai-copytoolkit'); ?>');
            resetTemplateForm();
        }
        
        $('#template-form input, #template-form textarea, #template-form select').prop('disabled', viewOnly);
        $('#modal-save').toggle(!viewOnly);
        
        $('#template-modal').show();
    }
    
    function closeTemplateModal() {
        $('#template-modal').hide();
        currentTemplateId = null;
    }
    
    function loadTemplateData(templateId) {
        // In a real implementation, you'd load template data via AJAX
        // For now, we'll just show a placeholder
        $('#template-name').val('Loading...');
    }
    
    function resetTemplateForm() {
        $('#template-form')[0].reset();
        $('#template-id').val('');
    }
    
    function saveTemplate() {
        const formData = {
            action: 'ai_copytoolkit_save_template',
            nonce: aiCopyToolkit.nonce,
            template: {
                id: $('#template-id').val(),
                name: $('#template-name').val(),
                description: $('#template-description').val(),
                category: $('#template-category').val(),
                content_type: $('#template-content-type').val(),
                prompt_template: $('#template-prompt').val(),
                parameters: {}
            }
        };
        
        $.post(aiCopyToolkit.ajaxUrl, formData, function(response) {
            if (response.success) {
                showNotice('<?php _e('Template saved successfully!', 'ai-copytoolkit'); ?>', 'success');
                closeTemplateModal();
                location.reload();
            } else {
                showNotice(response.data || '<?php _e('Failed to save template', 'ai-copytoolkit'); ?>', 'error');
            }
        });
    }
    
    function deleteTemplate(templateId) {
        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_delete_template',
            nonce: aiCopyToolkit.nonce,
            template_id: templateId
        }, function(response) {
            if (response.success) {
                showNotice('<?php _e('Template deleted successfully!', 'ai-copytoolkit'); ?>', 'success');
                $('[data-template-id="' + templateId + '"]').fadeOut();
            } else {
                showNotice(response.data || '<?php _e('Failed to delete template', 'ai-copytoolkit'); ?>', 'error');
            }
        });
    }
    
    function showNotice(message, type) {
        const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        $('.ai-copytoolkit-templates .wp-heading-inline').after($notice);
        
        setTimeout(function() {
            $notice.fadeOut();
        }, 3000);
    }
});
</script>
