<!DOCTYPE html>
<html>
<head>
    <title>Quick Thumbnail Creator - AI CopyToolkit</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .thumbnail-creator { 
            width: 300px; 
            margin: 20px auto; 
            text-align: center; 
            border: 2px solid #ddd; 
            padding: 20px; 
        }
        .thumbnail-preview { 
            width: 80px; 
            height: 80px; 
            margin: 20px auto; 
            background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .robot-icon { font-size: 24px; margin-bottom: 4px; }
        .ai-text { font-size: 14px; }
        button { 
            background: #0073aa; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 10px;
        }
        .instructions { 
            background: #f0f8ff; 
            padding: 15px; 
            border-radius: 4px; 
            margin: 20px 0; 
        }
    </style>
</head>
<body>
    <h1>🎨 AI CopyToolkit Thumbnail Creator</h1>
    
    <div class="instructions">
        <h3>📋 Quick Instructions:</h3>
        <ol>
            <li>Take a screenshot of the preview below</li>
            <li>Crop to exactly 80x80 pixels</li>
            <li>Save as PNG (under 50KB)</li>
            <li>Upload to ThemeForest</li>
        </ol>
    </div>
    
    <div class="thumbnail-creator">
        <h3>Thumbnail Preview (80x80px)</h3>
        
        <div class="thumbnail-preview" id="thumbnail">
            <div class="robot-icon">🤖</div>
            <div class="ai-text">AI</div>
        </div>
        
        <button onclick="changeStyle(1)">Style 1: Blue</button>
        <button onclick="changeStyle(2)">Style 2: Purple</button>
        <button onclick="changeStyle(3)">Style 3: Green</button>
        
        <div style="margin-top: 20px;">
            <h4>How to Save:</h4>
            <p>1. Right-click on preview above</p>
            <p>2. "Save image as..." or take screenshot</p>
            <p>3. Crop to 80x80 pixels exactly</p>
            <p>4. Save as PNG format</p>
        </div>
    </div>
    
    <div class="instructions">
        <h3>🛠️ Alternative Methods:</h3>
        
        <h4>Method 1: Canva (Recommended)</h4>
        <ol>
            <li>Go to <a href="https://canva.com" target="_blank">canva.com</a></li>
            <li>Create custom size: 80x80 pixels</li>
            <li>Add blue gradient background</li>
            <li>Add robot emoji 🤖</li>
            <li>Add "AI" text in white</li>
            <li>Download as PNG</li>
        </ol>
        
        <h4>Method 2: Online Icon Generator</h4>
        <ol>
            <li>Go to <a href="https://favicon.io/emoji-favicons/robot/" target="_blank">favicon.io</a></li>
            <li>Choose robot emoji</li>
            <li>Download 32x32 or larger</li>
            <li>Resize to 80x80 pixels</li>
        </ol>
        
        <h4>Method 3: Simple Paint/GIMP</h4>
        <ol>
            <li>Create 80x80 pixel image</li>
            <li>Fill with blue color (#0073aa)</li>
            <li>Add white robot symbol</li>
            <li>Add "AI" text</li>
            <li>Save as PNG</li>
        </ol>
    </div>
    
    <script>
        function changeStyle(style) {
            const thumbnail = document.getElementById('thumbnail');
            
            switch(style) {
                case 1:
                    thumbnail.style.background = 'linear-gradient(135deg, #0073aa 0%, #005177 100%)';
                    break;
                case 2:
                    thumbnail.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    break;
                case 3:
                    thumbnail.style.background = 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)';
                    break;
            }
        }
    </script>
    
    <div style="text-align: center; margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 4px;">
        <h3>⚡ Quick Action:</h3>
        <p><strong>Right now:</strong> Take a screenshot of the blue preview above, crop to 80x80px, save as PNG!</p>
        <p><strong>File size check:</strong> Must be under 50KB (usually is for simple icons)</p>
    </div>
    
</body>
</html>
