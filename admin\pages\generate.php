<?php
/**
 * Content generation page template
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get plugin settings
$settings = AI_CopyToolkit::get_settings();
$api_configured = !empty($settings['api_key']);

// Get available templates
$content_generator = AI_CopyToolkit_Content_Generator::instance();
$templates = $content_generator->get_templates();

// Group templates by category
$template_categories = array();
foreach ($templates as $template) {
    $template_categories[$template->category][] = $template;
}
?>

<div class="wrap ai-copytoolkit-generate">
    <h1 class="wp-heading-inline">
        <?php _e('Generate Content', 'ai-copytoolkit'); ?>
    </h1>
    
    <?php if (!$api_configured): ?>
        <div class="notice notice-error">
            <p>
                <strong><?php _e('API Key Required', 'ai-copytoolkit'); ?></strong>
                <?php printf(
                    __('Please <a href="%s">configure your OpenRouter API key</a> before generating content.', 'ai-copytoolkit'),
                    admin_url('admin.php?page=ai-copytoolkit-settings')
                ); ?>
            </p>
        </div>
    <?php else: ?>
        
        <div class="ai-copytoolkit-generate-container">
            <!-- Template Selection -->
            <div class="ai-copytoolkit-card template-selection">
                <h2><?php _e('Choose a Template', 'ai-copytoolkit'); ?></h2>
                
                <div class="template-tabs">
                    <button class="template-tab active" data-tab="templates"><?php _e('Templates', 'ai-copytoolkit'); ?></button>
                    <button class="template-tab" data-tab="custom"><?php _e('Custom Prompt', 'ai-copytoolkit'); ?></button>
                </div>
                
                <!-- Template Tab -->
                <div class="template-tab-content" id="templates-tab">
                    <?php if (!empty($template_categories)): ?>
                        <div class="template-categories">
                            <?php foreach ($template_categories as $category => $category_templates): ?>
                                <div class="template-category">
                                    <h3><?php echo esc_html(ucwords(str_replace('_', ' ', $category))); ?></h3>
                                    <div class="template-grid">
                                        <?php foreach ($category_templates as $template): ?>
                                            <div class="template-card" data-template-id="<?php echo $template->id; ?>">
                                                <div class="template-header">
                                                    <h4><?php echo esc_html($template->name); ?></h4>
                                                    <?php if ($template->is_default): ?>
                                                        <span class="template-badge"><?php _e('Default', 'ai-copytoolkit'); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="template-description">
                                                    <?php echo esc_html($template->description); ?>
                                                </div>
                                                <div class="template-meta">
                                                    <span class="template-type"><?php echo esc_html(ucwords(str_replace('_', ' ', $template->content_type))); ?></span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="no-templates">
                            <p><?php _e('No templates available.', 'ai-copytoolkit'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Custom Prompt Tab -->
                <div class="template-tab-content" id="custom-tab" style="display: none;">
                    <div class="custom-prompt-section">
                        <label for="custom-prompt"><?php _e('Enter your custom prompt:', 'ai-copytoolkit'); ?></label>
                        <textarea id="custom-prompt" rows="6" placeholder="<?php _e('Describe what kind of content you want to generate...', 'ai-copytoolkit'); ?>"></textarea>
                        <p class="description">
                            <?php _e('Be as specific as possible for better results. Include details about tone, length, target audience, and any specific requirements.', 'ai-copytoolkit'); ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Template Parameters -->
            <div class="ai-copytoolkit-card template-parameters" id="template-parameters" style="display: none;">
                <h2><?php _e('Template Parameters', 'ai-copytoolkit'); ?></h2>
                <div id="parameters-form">
                    <!-- Parameters will be loaded dynamically -->
                </div>
            </div>
            
            <!-- Generation Settings -->
            <div class="ai-copytoolkit-card generation-settings">
                <h2><?php _e('Generation Settings', 'ai-copytoolkit'); ?></h2>

                <div class="settings-grid">
                    <div class="setting-group">
                        <label for="model-select"><?php _e('AI Model:', 'ai-copytoolkit'); ?></label>
                        <div class="model-select-wrapper">
                            <select id="model-select">
                                <option value=""><?php _e('Loading models...', 'ai-copytoolkit'); ?></option>
                            </select>
                            <button type="button" id="refresh-models" class="button button-small">
                                <span class="dashicons dashicons-update"></span>
                                <?php _e('Refresh', 'ai-copytoolkit'); ?>
                            </button>
                        </div>
                        <p class="description"><?php _e('Different models excel at different types of content.', 'ai-copytoolkit'); ?></p>
                        <div id="model-info" class="model-info" style="display: none;">
                            <div class="model-details">
                                <span class="model-name"></span>
                                <span class="model-context"></span>
                                <span class="model-pricing"></span>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <label for="max-tokens"><?php _e('Max Tokens:', 'ai-copytoolkit'); ?></label>
                        <div class="token-input-group">
                            <input type="number" id="max-tokens" value="<?php echo esc_attr($settings['max_tokens'] ?? 500); ?>" min="50" max="4000" step="50">
                            <div class="token-presets">
                                <button type="button" class="token-preset" data-tokens="100"><?php _e('Short', 'ai-copytoolkit'); ?></button>
                                <button type="button" class="token-preset" data-tokens="500"><?php _e('Medium', 'ai-copytoolkit'); ?></button>
                                <button type="button" class="token-preset" data-tokens="1000"><?php _e('Long', 'ai-copytoolkit'); ?></button>
                            </div>
                        </div>
                        <p class="description">
                            <?php _e('Maximum length of generated content (roughly 4 characters per token).', 'ai-copytoolkit'); ?>
                            <span id="token-estimate"></span>
                        </p>
                    </div>

                    <div class="setting-group">
                        <label for="temperature"><?php _e('Temperature:', 'ai-copytoolkit'); ?></label>
                        <div class="temperature-control">
                            <input type="range" id="temperature" min="0" max="1" step="0.1" value="<?php echo esc_attr($settings['temperature'] ?? 0.7); ?>">
                            <span class="temperature-value"><?php echo esc_attr($settings['temperature'] ?? 0.7); ?></span>
                        </div>
                        <div class="temperature-presets">
                            <button type="button" class="temp-preset" data-temp="0.3"><?php _e('Focused', 'ai-copytoolkit'); ?></button>
                            <button type="button" class="temp-preset" data-temp="0.7"><?php _e('Balanced', 'ai-copytoolkit'); ?></button>
                            <button type="button" class="temp-preset" data-temp="0.9"><?php _e('Creative', 'ai-copytoolkit'); ?></button>
                        </div>
                        <p class="description">
                            <?php _e('Controls creativity: Lower = more focused, Higher = more creative.', 'ai-copytoolkit'); ?>
                            <span id="temperature-description"></span>
                        </p>
                    </div>

                    <div class="setting-group">
                        <label><?php _e('Quick Settings:', 'ai-copytoolkit'); ?></label>
                        <div class="quick-settings">
                            <button type="button" class="quick-setting" data-preset="factual">
                                <?php _e('Factual Content', 'ai-copytoolkit'); ?>
                            </button>
                            <button type="button" class="quick-setting" data-preset="marketing">
                                <?php _e('Marketing Copy', 'ai-copytoolkit'); ?>
                            </button>
                            <button type="button" class="quick-setting" data-preset="creative">
                                <?php _e('Creative Writing', 'ai-copytoolkit'); ?>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="settings-actions">
                    <button type="button" id="save-settings-preset" class="button button-secondary">
                        <?php _e('Save as Default', 'ai-copytoolkit'); ?>
                    </button>
                    <button type="button" id="reset-settings" class="button button-secondary">
                        <?php _e('Reset to Defaults', 'ai-copytoolkit'); ?>
                    </button>
                </div>
            </div>
            
            <!-- Generate Button -->
            <div class="ai-copytoolkit-card generate-action">
                <button id="generate-content" class="button button-primary button-large" disabled>
                    <span class="dashicons dashicons-edit-large"></span>
                    <?php _e('Generate Content', 'ai-copytoolkit'); ?>
                </button>
                <div class="generation-status" id="generation-status"></div>
            </div>
            
            <!-- Generated Content -->
            <div class="ai-copytoolkit-card generated-content" id="generated-content" style="display: none;">
                <div class="content-header">
                    <h2><?php _e('Generated Content', 'ai-copytoolkit'); ?></h2>
                    <div class="content-actions">
                        <button id="copy-content" class="button button-secondary">
                            <span class="dashicons dashicons-admin-page"></span>
                            <?php _e('Copy', 'ai-copytoolkit'); ?>
                        </button>
                        <button id="regenerate-content" class="button button-secondary">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Regenerate', 'ai-copytoolkit'); ?>
                        </button>
                        <button id="save-content" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            <?php _e('Save to History', 'ai-copytoolkit'); ?>
                        </button>
                    </div>
                </div>
                
                <div class="content-body">
                    <textarea id="content-output" rows="10" readonly></textarea>
                </div>
                
                <div class="content-meta">
                    <div class="meta-item">
                        <strong><?php _e('Model Used:', 'ai-copytoolkit'); ?></strong>
                        <span id="meta-model"></span>
                    </div>
                    <div class="meta-item">
                        <strong><?php _e('Tokens Used:', 'ai-copytoolkit'); ?></strong>
                        <span id="meta-tokens"></span>
                    </div>
                    <div class="meta-item">
                        <strong><?php _e('Generation Time:', 'ai-copytoolkit'); ?></strong>
                        <span id="meta-time"></span>
                    </div>
                </div>
            </div>
        </div>
        
    <?php endif; ?>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    let selectedTemplateId = null;
    let currentGeneration = null;
    
    // Load available models
    loadAvailableModels();
    
    // Template tab switching
    $('.template-tab').on('click', function() {
        const tab = $(this).data('tab');
        $('.template-tab').removeClass('active');
        $(this).addClass('active');
        $('.template-tab-content').hide();
        $('#' + tab + '-tab').show();
        
        // Reset selection
        selectedTemplateId = null;
        $('.template-card').removeClass('selected');
        $('#template-parameters').hide();
        updateGenerateButton();
    });
    
    // Template selection
    $('.template-card').on('click', function() {
        $('.template-card').removeClass('selected');
        $(this).addClass('selected');
        selectedTemplateId = $(this).data('template-id');
        loadTemplateParameters(selectedTemplateId);
        updateGenerateButton();
    });
    
    // Temperature slider
    $('#temperature').on('input', function() {
        $('.temperature-value').text($(this).val());
    });
    
    // Generate content
    $('#generate-content').on('click', function() {
        generateContent();
    });
    
    // Copy content
    $('#copy-content').on('click', function() {
        const content = $('#content-output').val();
        navigator.clipboard.writeText(content).then(function() {
            showNotice(aiCopyToolkit.strings.copied, 'success');
        }).catch(function() {
            showNotice(aiCopyToolkit.strings.copy_failed, 'error');
        });
    });
    
    // Regenerate content
    $('#regenerate-content').on('click', function() {
        generateContent();
    });
    
    function loadAvailableModels(forceRefresh = false) {
        const $select = $('#model-select');
        const $refreshBtn = $('#refresh-models');

        // Show loading state
        if (forceRefresh) {
            $refreshBtn.prop('disabled', true);
            $refreshBtn.find('.dashicons').addClass('spin');
        }

        $select.html('<option value=""><?php _e('Loading models...', 'ai-copytoolkit'); ?></option>');

        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_get_models',
            nonce: aiCopyToolkit.nonce,
            force_refresh: forceRefresh
        }, function(response) {
            $select.empty();

            if (response.success && response.data.length > 0) {
                // Add models from API
                response.data.forEach(function(model) {
                    $select.append($('<option>', {
                        value: model.id,
                        text: model.name || model.id,
                        'data-context': model.context_length || '',
                        'data-pricing': model.pricing || ''
                    }));
                });

                if (forceRefresh) {
                    showNotice('<?php _e('Models refreshed successfully', 'ai-copytoolkit'); ?>', 'success');
                }
            } else {
                // Add fallback models
                addFallbackModels($select);
                if (forceRefresh) {
                    showNotice('<?php _e('Using fallback models. Check your API key.', 'ai-copytoolkit'); ?>', 'warning');
                }
            }

            // Set default model
            const defaultModel = '<?php echo esc_js($settings['default_model'] ?? 'openai/gpt-3.5-turbo'); ?>';
            $select.val(defaultModel);

            // Load saved settings
            const saved = localStorage.getItem('aiCopyToolkitSettings');
            if (saved) {
                try {
                    const settings = JSON.parse(saved);
                    if (settings.model) {
                        $select.val(settings.model);
                    }
                } catch (e) {
                    console.log('Failed to load saved model');
                }
            }

            updateModelInfo();

        }).fail(function() {
            // Add fallback models if API fails
            $select.empty();
            addFallbackModels($select);

            if (forceRefresh) {
                showNotice('<?php _e('Failed to load models. Using defaults.', 'ai-copytoolkit'); ?>', 'error');
            }

            // Set default model
            const defaultModel = '<?php echo esc_js($settings['default_model'] ?? 'openai/gpt-3.5-turbo'); ?>';
            $select.val(defaultModel);

        }).always(function() {
            // Reset loading state
            if ($refreshBtn.length) {
                $refreshBtn.prop('disabled', false);
                $refreshBtn.find('.dashicons').removeClass('spin');
            }
        });
    }

    function addFallbackModels($select) {
        const fallbackModels = [
            // === FREE MODELS ===
            {
                id: 'free',
                name: '🆓 === FREE MODELS ===',
                context: '',
                pricing: 'Free',
                disabled: true
            },
            {
                id: 'microsoft/wizardlm-2-8x22b',
                name: '🆓 WizardLM-2 8x22B',
                context: '65K',
                pricing: 'Free'
            },
            {
                id: 'microsoft/wizardlm-2-7b',
                name: '🆓 WizardLM-2 7B',
                context: '32K',
                pricing: 'Free'
            },
            {
                id: 'huggingfaceh4/zephyr-7b-beta',
                name: '🆓 Zephyr 7B Beta',
                context: '32K',
                pricing: 'Free'
            },
            {
                id: 'openchat/openchat-7b',
                name: '🆓 OpenChat 7B',
                context: '8K',
                pricing: 'Free'
            },
            {
                id: 'gryphe/mythomist-7b',
                name: '🆓 MythoMist 7B',
                context: '32K',
                pricing: 'Free'
            },
            {
                id: 'undi95/toppy-m-7b',
                name: '🆓 Toppy M 7B',
                context: '4K',
                pricing: 'Free'
            },
            {
                id: 'google/gemma-7b-it',
                name: '🆓 Gemma 7B IT',
                context: '8K',
                pricing: 'Free'
            },
            {
                id: 'mistralai/mistral-small-3.2-24b:free',
                name: '🆓 Mistral Small 3.2 24B',
                context: '128K',
                pricing: 'Free'
            },
            {
                id: 'kimi/kimi-dev-72b:free',
                name: '🆓 Kimi Dev 72B',
                context: '200K',
                pricing: 'Free'
            },
            {
                id: 'deepseek/deepseek-r1-0528:free',
                name: '🆓 DeepSeek R1 0528',
                context: '128K',
                pricing: 'Free'
            },

            // === OPENAI MODELS ===
            {
                id: 'openai-divider',
                name: '🤖 === OPENAI MODELS ===',
                context: '',
                pricing: '',
                disabled: true
            },
            {
                id: 'openai/gpt-4o',
                name: 'GPT-4o (Latest)',
                context: '128K',
                pricing: '$5/$15 per 1M'
            },
            {
                id: 'openai/gpt-4o-mini',
                name: 'GPT-4o Mini',
                context: '128K',
                pricing: '$0.15/$0.6 per 1M'
            },
            {
                id: 'openai/gpt-4-turbo',
                name: 'GPT-4 Turbo',
                context: '128K',
                pricing: '$10/$30 per 1M'
            },
            {
                id: 'openai/gpt-4',
                name: 'GPT-4',
                context: '8K',
                pricing: '$30/$60 per 1M'
            },
            {
                id: 'openai/gpt-3.5-turbo',
                name: 'GPT-3.5 Turbo',
                context: '16K',
                pricing: '$0.5/$1.5 per 1M'
            },

            // === ANTHROPIC MODELS ===
            {
                id: 'anthropic-divider',
                name: '🧠 === ANTHROPIC MODELS ===',
                context: '',
                pricing: '',
                disabled: true
            },
            {
                id: 'anthropic/claude-3.5-sonnet',
                name: 'Claude 3.5 Sonnet (Latest)',
                context: '200K',
                pricing: '$3/$15 per 1M'
            },
            {
                id: 'anthropic/claude-3-opus',
                name: 'Claude 3 Opus',
                context: '200K',
                pricing: '$15/$75 per 1M'
            },
            {
                id: 'anthropic/claude-3-sonnet',
                name: 'Claude 3 Sonnet',
                context: '200K',
                pricing: '$3/$15 per 1M'
            },
            {
                id: 'anthropic/claude-3-haiku',
                name: 'Claude 3 Haiku',
                context: '200K',
                pricing: '$0.25/$1.25 per 1M'
            },

            // === GOOGLE MODELS ===
            {
                id: 'google-divider',
                name: '🔍 === GOOGLE MODELS ===',
                context: '',
                pricing: '',
                disabled: true
            },
            {
                id: 'google/gemini-pro-1.5',
                name: 'Gemini Pro 1.5',
                context: '2M',
                pricing: '$3.5/$10.5 per 1M'
            },
            {
                id: 'google/gemini-pro',
                name: 'Gemini Pro',
                context: '32K',
                pricing: '$0.5/$1.5 per 1M'
            },
            {
                id: 'google/gemini-flash-1.5',
                name: 'Gemini Flash 1.5',
                context: '1M',
                pricing: '$0.075/$0.3 per 1M'
            },
            {
                id: 'google/palm-2-chat-bison',
                name: 'PaLM 2 Chat',
                context: '8K',
                pricing: '$0.5/$1 per 1M'
            },

            // === META LLAMA MODELS ===
            {
                id: 'meta-divider',
                name: '🦙 === META LLAMA MODELS ===',
                context: '',
                pricing: '',
                disabled: true
            },
            {
                id: 'meta-llama/llama-3.1-405b-instruct',
                name: 'LLaMA 3.1 405B Instruct',
                context: '128K',
                pricing: '$5/$15 per 1M'
            },
            {
                id: 'meta-llama/llama-3.1-70b-instruct',
                name: 'LLaMA 3.1 70B Instruct',
                context: '128K',
                pricing: '$0.9/$0.9 per 1M'
            },
            {
                id: 'meta-llama/llama-3.1-8b-instruct',
                name: 'LLaMA 3.1 8B Instruct',
                context: '128K',
                pricing: '$0.1/$0.1 per 1M'
            },
            {
                id: 'meta-llama/llama-3-70b-instruct',
                name: 'LLaMA 3 70B Instruct',
                context: '8K',
                pricing: '$0.9/$0.9 per 1M'
            },
            {
                id: 'meta-llama/llama-3-8b-instruct',
                name: 'LLaMA 3 8B Instruct',
                context: '8K',
                pricing: '$0.1/$0.1 per 1M'
            },
            {
                id: 'meta-llama/llama-2-70b-chat',
                name: 'LLaMA 2 70B Chat',
                context: '4K',
                pricing: '$0.7/$0.8 per 1M'
            },

            // === MISTRAL MODELS ===
            {
                id: 'mistral-divider',
                name: '🌪️ === MISTRAL MODELS ===',
                context: '',
                pricing: '',
                disabled: true
            },
            {
                id: 'mistralai/mistral-large',
                name: 'Mistral Large',
                context: '128K',
                pricing: '$4/$12 per 1M'
            },
            {
                id: 'mistralai/mistral-medium',
                name: 'Mistral Medium',
                context: '32K',
                pricing: '$2.7/$8.1 per 1M'
            },
            {
                id: 'mistralai/mistral-small',
                name: 'Mistral Small',
                context: '32K',
                pricing: '$1/$3 per 1M'
            },
            {
                id: 'mistralai/mistral-7b-instruct',
                name: 'Mistral 7B Instruct',
                context: '32K',
                pricing: '$0.1/$0.1 per 1M'
            },
            {
                id: 'mistralai/mixtral-8x7b-instruct',
                name: 'Mixtral 8x7B Instruct',
                context: '32K',
                pricing: '$0.24/$0.24 per 1M'
            },
            {
                id: 'mistralai/mixtral-8x22b-instruct',
                name: 'Mixtral 8x22B Instruct',
                context: '64K',
                pricing: '$0.65/$0.65 per 1M'
            },

            // === COHERE MODELS ===
            {
                id: 'cohere-divider',
                name: '🔗 === COHERE MODELS ===',
                context: '',
                pricing: '',
                disabled: true
            },
            {
                id: 'cohere/command-r-plus',
                name: 'Command R+',
                context: '128K',
                pricing: '$3/$15 per 1M'
            },
            {
                id: 'cohere/command-r',
                name: 'Command R',
                context: '128K',
                pricing: '$0.5/$1.5 per 1M'
            },
            {
                id: 'cohere/command',
                name: 'Command',
                context: '4K',
                pricing: '$1/$2 per 1M'
            },

            // === SPECIALIZED MODELS ===
            {
                id: 'specialized-divider',
                name: '⚡ === SPECIALIZED MODELS ===',
                context: '',
                pricing: '',
                disabled: true
            },
            {
                id: 'perplexity/llama-3.1-sonar-large-128k-online',
                name: 'Perplexity Sonar Large (Online)',
                context: '128K',
                pricing: '$5/$5 per 1M'
            },
            {
                id: 'perplexity/llama-3.1-sonar-small-128k-online',
                name: 'Perplexity Sonar Small (Online)',
                context: '128K',
                pricing: '$0.2/$0.2 per 1M'
            },
            {
                id: 'databricks/dbrx-instruct',
                name: 'DBRX Instruct',
                context: '32K',
                pricing: '$0.75/$2.25 per 1M'
            },
            {
                id: 'deepseek/deepseek-coder',
                name: 'DeepSeek Coder',
                context: '16K',
                pricing: '$0.14/$0.28 per 1M'
            },
            {
                id: 'qwen/qwen-2-72b-instruct',
                name: 'Qwen 2 72B Instruct',
                context: '32K',
                pricing: '$0.56/$0.77 per 1M'
            },

            // === CREATIVE & ROLEPLAY MODELS ===
            {
                id: 'creative-divider',
                name: '🎨 === CREATIVE & ROLEPLAY ===',
                context: '',
                pricing: '',
                disabled: true
            },
            {
                id: 'anthropic/claude-3-opus:beta',
                name: 'Claude 3 Opus (Creative)',
                context: '200K',
                pricing: '$15/$75 per 1M'
            },
            {
                id: 'gryphe/mythomax-l2-13b',
                name: 'MythoMax L2 13B',
                context: '4K',
                pricing: '$0.1/$0.1 per 1M'
            },
            {
                id: 'pygmalionai/mythalion-13b',
                name: 'Mythalion 13B',
                context: '8K',
                pricing: '$0.1/$0.1 per 1M'
            },

            // === CODING MODELS ===
            {
                id: 'coding-divider',
                name: '💻 === CODING MODELS ===',
                context: '',
                pricing: '',
                disabled: true
            },
            {
                id: 'openai/gpt-4o-mini-2024-07-18',
                name: 'GPT-4o Mini (Code)',
                context: '128K',
                pricing: '$0.15/$0.6 per 1M'
            },
            {
                id: 'anthropic/claude-3-haiku:beta',
                name: 'Claude 3 Haiku (Code)',
                context: '200K',
                pricing: '$0.25/$1.25 per 1M'
            },
            {
                id: 'codellama/codellama-70b-instruct',
                name: 'CodeLlama 70B Instruct',
                context: '4K',
                pricing: '$0.7/$0.8 per 1M'
            },
            {
                id: 'phind/phind-codellama-34b',
                name: 'Phind CodeLlama 34B',
                context: '4K',
                pricing: '$0.7/$0.7 per 1M'
            }
        ];

        fallbackModels.forEach(function(model) {
            const option = $('<option>', {
                value: model.id,
                text: model.name,
                'data-context': model.context,
                'data-pricing': model.pricing
            });

            // Disable divider options
            if (model.disabled) {
                option.prop('disabled', true);
                option.css({
                    'font-weight': 'bold',
                    'background-color': '#f0f0f0',
                    'color': '#666'
                });
            }

            $select.append(option);
        });
    }
    
    function loadTemplateParameters(templateId) {
        $('#template-parameters').show();
        $('#parameters-form').html('<p><span class="spinner is-active"></span> Loading parameters...</p>');

        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_get_template_parameters',
            nonce: aiCopyToolkit.nonce,
            template_id: templateId
        }, function(response) {
            if (response.success) {
                const template = response.data.template;
                const parameters = response.data.parameters;

                let formHtml = '<h3>' + template.name + '</h3>';
                formHtml += '<p class="description">' + template.description + '</p>';

                if (Object.keys(parameters).length > 0) {
                    formHtml += '<div class="template-parameters-form">';

                    Object.keys(parameters).forEach(function(paramKey) {
                        const param = parameters[paramKey];
                        formHtml += '<div class="parameter-group">';
                        formHtml += '<label for="param_' + paramKey + '">' + (param.label || paramKey) + ':</label>';

                        if (param.type === 'select') {
                            formHtml += '<select id="param_' + paramKey + '" name="' + paramKey + '">';
                            param.options.forEach(function(option) {
                                const selected = option === param.default ? ' selected' : '';
                                formHtml += '<option value="' + option + '"' + selected + '>' + option + '</option>';
                            });
                            formHtml += '</select>';
                        } else if (param.type === 'textarea') {
                            formHtml += '<textarea id="param_' + paramKey + '" name="' + paramKey + '" rows="3">' + (param.default || '') + '</textarea>';
                        } else if (param.type === 'number') {
                            formHtml += '<input type="number" id="param_' + paramKey + '" name="' + paramKey + '" value="' + (param.default || '') + '">';
                        } else {
                            formHtml += '<input type="text" id="param_' + paramKey + '" name="' + paramKey + '" value="' + (param.default || '') + '">';
                        }

                        if (param.required) {
                            formHtml += '<span class="required">*</span>';
                        }

                        formHtml += '</div>';
                    });

                    formHtml += '</div>';
                } else {
                    formHtml += '<p>This template has no configurable parameters.</p>';
                }

                $('#parameters-form').html(formHtml);
            } else {
                $('#parameters-form').html('<p class="error">Failed to load template parameters.</p>');
            }
        }).fail(function() {
            $('#parameters-form').html('<p class="error">Failed to load template parameters.</p>');
        });
    }
    
    function updateGenerateButton() {
        const hasTemplate = selectedTemplateId !== null;
        const hasCustomPrompt = $('#custom-prompt').val().trim() !== '';
        const canGenerate = hasTemplate || hasCustomPrompt;
        
        $('#generate-content').prop('disabled', !canGenerate);
    }
    
    function generateContent() {
        const $button = $('#generate-content');
        const $status = $('#generation-status');

        $button.prop('disabled', true);
        $status.html('<span class="spinner is-active"></span> ' + aiCopyToolkit.strings.generating);

        // Collect template parameters
        const templateParameters = {};
        $('.template-parameters-form input, .template-parameters-form textarea, .template-parameters-form select').each(function() {
            const $field = $(this);
            templateParameters[$field.attr('name')] = $field.val();
        });

        const data = {
            action: 'ai_copytoolkit_generate_content',
            nonce: aiCopyToolkit.nonce,
            template_id: selectedTemplateId || 0,
            custom_prompt: $('#custom-prompt').val(),
            template_parameters: templateParameters,
            parameters: {
                model: $('#model-select').val(),
                max_tokens: parseInt($('#max-tokens').val()),
                temperature: parseFloat($('#temperature').val())
            }
        };

        $.post(aiCopyToolkit.ajaxUrl, data, function(response) {
            if (response.success) {
                displayGeneratedContent(response.data);
                showNotice(aiCopyToolkit.strings.success, 'success');
            } else {
                showNotice(response.data || aiCopyToolkit.strings.error, 'error');
            }
        }).fail(function() {
            showNotice(aiCopyToolkit.strings.error, 'error');
        }).always(function() {
            $button.prop('disabled', false);
            $status.empty();
            updateGenerateButton();
        });
    }
    
    function displayGeneratedContent(data) {
        $('#content-output').val(data.content);
        $('#meta-model').text(data.model);
        $('#meta-tokens').text(data.tokens_used);
        $('#meta-time').text(data.generation_time.toFixed(2) + 's');
        $('#generated-content').show();
        
        currentGeneration = data;
    }
    
    function showNotice(message, type) {
        const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        $('.ai-copytoolkit-generate .wp-heading-inline').after($notice);
        
        setTimeout(function() {
            $notice.fadeOut();
        }, 3000);
    }
    
    // Update generate button when custom prompt changes
    $('#custom-prompt').on('input', updateGenerateButton);

    // Initialize enhanced settings
    initializeSettings();
    bindSettingEvents();

    // Enhanced Settings Functions
    function initializeSettings() {
        // Load saved settings from localStorage
        loadSavedSettings();

        // Update temperature display
        updateTemperatureDisplay();

        // Update token estimate
        updateTokenEstimate();

        // Update temperature description
        updateTemperatureDescription();
    }

    function bindSettingEvents() {
        // Temperature slider
        $('#temperature').on('input', function() {
            $('.temperature-value').text($(this).val());
            updateTemperatureDescription();
            saveCurrentSettings();
        });

        // Max tokens input
        $('#max-tokens').on('input', function() {
            updateTokenEstimate();
            saveCurrentSettings();
        });

        // Model selection
        $('#model-select').on('change', function() {
            updateModelInfo();
            saveCurrentSettings();
        });

        // Token presets
        $('.token-preset').on('click', function() {
            const tokens = $(this).data('tokens');
            $('#max-tokens').val(tokens);
            updateTokenEstimate();
            saveCurrentSettings();
        });

        // Temperature presets
        $('.temp-preset').on('click', function() {
            const temp = $(this).data('temp');
            $('#temperature').val(temp);
            $('.temperature-value').text(temp);
            updateTemperatureDescription();
            saveCurrentSettings();
        });

        // Quick settings
        $('.quick-setting').on('click', function() {
            const preset = $(this).data('preset');
            applyQuickSetting(preset);
        });

        // Refresh models
        $('#refresh-models').on('click', function() {
            loadAvailableModels(true);
        });

        // Save settings preset
        $('#save-settings-preset').on('click', function() {
            saveSettingsAsDefault();
        });

        // Reset settings
        $('#reset-settings').on('click', function() {
            resetToDefaults();
        });
    }

    function updateTemperatureDisplay() {
        const temp = $('#temperature').val();
        $('.temperature-value').text(temp);
    }

    function updateTokenEstimate() {
        const tokens = parseInt($('#max-tokens').val());
        const estimate = Math.round(tokens * 4);
        $('#token-estimate').text('(~' + estimate + ' characters)');
    }

    function updateTemperatureDescription() {
        const temp = parseFloat($('#temperature').val());
        let description = '';

        if (temp <= 0.3) {
            description = '<?php _e('Very focused and deterministic', 'ai-copytoolkit'); ?>';
        } else if (temp <= 0.5) {
            description = '<?php _e('Focused with some variation', 'ai-copytoolkit'); ?>';
        } else if (temp <= 0.7) {
            description = '<?php _e('Balanced creativity and focus', 'ai-copytoolkit'); ?>';
        } else if (temp <= 0.9) {
            description = '<?php _e('Creative with good coherence', 'ai-copytoolkit'); ?>';
        } else {
            description = '<?php _e('Very creative and diverse', 'ai-copytoolkit'); ?>';
        }

        $('#temperature-description').text(' - ' + description);
    }

    function updateModelInfo() {
        const selectedModel = $('#model-select').val();
        if (!selectedModel) {
            $('#model-info').hide();
            return;
        }

        // Show model information (you can expand this with actual model data)
        const modelInfo = getModelInfo(selectedModel);
        if (modelInfo) {
            $('.model-name').text(modelInfo.name);
            $('.model-context').text('Context: ' + modelInfo.context);
            $('.model-pricing').text('Cost: ' + modelInfo.pricing);
            $('#model-info').show();
        }
    }

    function getModelInfo(modelId) {
        // Get model info from the selected option
        const $selectedOption = $('#model-select option[value="' + modelId + '"]');

        if ($selectedOption.length && !$selectedOption.prop('disabled')) {
            const context = $selectedOption.data('context');
            const pricing = $selectedOption.data('pricing');
            const name = $selectedOption.text();

            return {
                name: name,
                context: context ? context + ' tokens' : 'Variable context',
                pricing: pricing || 'Pricing varies'
            };
        }

        // Fallback model information database
        const models = {
            'openai/gpt-4o': {
                name: 'GPT-4o (Latest)',
                context: '128K tokens',
                pricing: '$5/$15 per 1M tokens',
                description: 'Latest and most capable OpenAI model'
            },
            'openai/gpt-3.5-turbo': {
                name: 'GPT-3.5 Turbo',
                context: '16K tokens',
                pricing: '$0.5/$1.5 per 1M tokens',
                description: 'Fast and cost-effective for most tasks'
            },
            'anthropic/claude-3.5-sonnet': {
                name: 'Claude 3.5 Sonnet',
                context: '200K tokens',
                pricing: '$3/$15 per 1M tokens',
                description: 'Excellent for analysis and creative writing'
            },
            'anthropic/claude-3-haiku': {
                name: 'Claude 3 Haiku',
                context: '200K tokens',
                pricing: '$0.25/$1.25 per 1M tokens',
                description: 'Fast and affordable for simple tasks'
            },
            'meta-llama/llama-3.1-405b-instruct': {
                name: 'LLaMA 3.1 405B',
                context: '128K tokens',
                pricing: '$5/$15 per 1M tokens',
                description: 'Most capable open-source model'
            },
            'google/gemini-pro-1.5': {
                name: 'Gemini Pro 1.5',
                context: '2M tokens',
                pricing: '$3.5/$10.5 per 1M tokens',
                description: 'Massive context window for long documents'
            },
            'mistralai/mistral-large': {
                name: 'Mistral Large',
                context: '128K tokens',
                pricing: '$4/$12 per 1M tokens',
                description: 'High-performance European model'
            },
            'mistralai/mistral-small-3.2-24b:free': {
                name: 'Mistral Small 3.2 24B (Free)',
                context: '128K tokens',
                pricing: 'Free',
                description: 'Free high-quality model from Mistral'
            },
            'kimi/kimi-dev-72b:free': {
                name: 'Kimi Dev 72B (Free)',
                context: '200K tokens',
                pricing: 'Free',
                description: 'Free large model with massive context'
            },
            'deepseek/deepseek-r1-0528:free': {
                name: 'DeepSeek R1 0528 (Free)',
                context: '128K tokens',
                pricing: 'Free',
                description: 'Free reasoning-focused model'
            }
        };

        return models[modelId] || {
            name: 'Unknown Model',
            context: 'Variable context',
            pricing: 'Pricing varies'
        };
    }

    function applyQuickSetting(preset) {
        switch (preset) {
            case 'factual':
                $('#temperature').val(0.3);
                $('#max-tokens').val(300);
                break;
            case 'marketing':
                $('#temperature').val(0.7);
                $('#max-tokens').val(500);
                break;
            case 'creative':
                $('#temperature').val(0.9);
                $('#max-tokens').val(800);
                break;
        }

        updateTemperatureDisplay();
        updateTokenEstimate();
        updateTemperatureDescription();
        saveCurrentSettings();

        showNotice('<?php _e('Quick settings applied', 'ai-copytoolkit'); ?>', 'success');
    }

    function saveCurrentSettings() {
        const settings = {
            model: $('#model-select').val(),
            maxTokens: $('#max-tokens').val(),
            temperature: $('#temperature').val()
        };

        localStorage.setItem('aiCopyToolkitSettings', JSON.stringify(settings));
    }

    function loadSavedSettings() {
        const saved = localStorage.getItem('aiCopyToolkitSettings');
        if (saved) {
            try {
                const settings = JSON.parse(saved);
                if (settings.model) $('#model-select').val(settings.model);
                if (settings.maxTokens) $('#max-tokens').val(settings.maxTokens);
                if (settings.temperature) $('#temperature').val(settings.temperature);
            } catch (e) {
                console.log('Failed to load saved settings');
            }
        }
    }

    function saveSettingsAsDefault() {
        const settings = {
            model: $('#model-select').val(),
            maxTokens: $('#max-tokens').val(),
            temperature: $('#temperature').val()
        };

        $.post(aiCopyToolkit.ajaxUrl, {
            action: 'ai_copytoolkit_save_default_settings',
            nonce: aiCopyToolkit.nonce,
            settings: settings
        }, function(response) {
            if (response.success) {
                showNotice('<?php _e('Settings saved as default', 'ai-copytoolkit'); ?>', 'success');
            } else {
                showNotice('<?php _e('Failed to save settings', 'ai-copytoolkit'); ?>', 'error');
            }
        });
    }

    function resetToDefaults() {
        $('#model-select').val('openai/gpt-3.5-turbo');
        $('#max-tokens').val(500);
        $('#temperature').val(0.7);

        updateTemperatureDisplay();
        updateTokenEstimate();
        updateTemperatureDescription();
        updateModelInfo();
        saveCurrentSettings();

        showNotice('<?php _e('Settings reset to defaults', 'ai-copytoolkit'); ?>', 'success');
    }

    // Model recommendation system
    function getRecommendedModels(contentType) {
        const recommendations = {
            'ecommerce': [
                'openai/gpt-4o-mini',
                'anthropic/claude-3-haiku',
                'openai/gpt-3.5-turbo'
            ],
            'advertising': [
                'openai/gpt-4o',
                'anthropic/claude-3.5-sonnet',
                'mistralai/mistral-large'
            ],
            'email': [
                'openai/gpt-4o-mini',
                'anthropic/claude-3-haiku',
                'openai/gpt-3.5-turbo'
            ],
            'social': [
                'openai/gpt-4o',
                'anthropic/claude-3.5-sonnet',
                'meta-llama/llama-3.1-70b-instruct'
            ],
            'creative': [
                'anthropic/claude-3-opus',
                'openai/gpt-4o',
                'meta-llama/llama-3.1-405b-instruct'
            ],
            'technical': [
                'openai/gpt-4o',
                'deepseek/deepseek-coder',
                'codellama/codellama-70b-instruct'
            ],
            'long-form': [
                'google/gemini-pro-1.5',
                'anthropic/claude-3.5-sonnet',
                'meta-llama/llama-3.1-405b-instruct'
            ]
        };

        return recommendations[contentType] || recommendations['ecommerce'];
    }

    // Add model recommendation tooltip
    function showModelRecommendation(templateCategory) {
        const recommended = getRecommendedModels(templateCategory);
        const currentModel = $('#model-select').val();

        if (recommended.includes(currentModel)) {
            showNotice('✅ ' + '<?php _e('Great choice! This model works well for this content type.', 'ai-copytoolkit'); ?>', 'success');
        } else {
            const modelNames = recommended.map(id => {
                const option = $('#model-select option[value="' + id + '"]');
                return option.length ? option.text() : id;
            });

            showNotice('💡 ' + '<?php _e('Recommended models for this content type:', 'ai-copytoolkit'); ?> ' + modelNames.slice(0, 2).join(', '), 'info');
        }
    }

    // Enhanced model information display
    function displayModelCapabilities(modelId) {
        const capabilities = {
            'openai/gpt-4o': ['💬 Conversational', '📝 Writing', '🔍 Analysis', '💻 Coding', '🌍 Multilingual'],
            'anthropic/claude-3.5-sonnet': ['📚 Long-form', '🎨 Creative', '🔍 Analysis', '💻 Coding', '📊 Data'],
            'anthropic/claude-3-haiku': ['⚡ Fast', '💰 Affordable', '📝 Writing', '💬 Chat', '🔍 Simple Analysis'],
            'meta-llama/llama-3.1-405b-instruct': ['🧠 Reasoning', '📚 Knowledge', '💻 Coding', '🔍 Analysis', '🌍 Multilingual'],
            'google/gemini-pro-1.5': ['📄 Long Documents', '🔍 Analysis', '💻 Coding', '📊 Data Processing', '🌍 Multilingual'],
            'mistralai/mistral-large': ['🇪🇺 European', '💻 Coding', '🔍 Analysis', '📝 Writing', '🌍 Multilingual']
        };

        const modelCaps = capabilities[modelId];
        if (modelCaps) {
            const capsHtml = modelCaps.map(cap => '<span class="capability-tag">' + cap + '</span>').join('');
            $('.model-details').append('<div class="model-capabilities">' + capsHtml + '</div>');
        }
    }
});
</script>
