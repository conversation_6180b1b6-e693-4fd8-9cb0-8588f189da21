<!DOCTYPE html>
<html>
<head>
    <title>Instant Thumbnail Generator</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f0f0f0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .thumbnail {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 20px auto;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .robot-icon { 
            font-size: 28px; 
            margin-bottom: 2px; 
        }
        
        .ai-text { 
            font-size: 12px; 
            font-weight: bold;
        }
        
        .preview {
            width: 590px;
            height: 300px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
        }
        
        .preview-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .preview-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .preview-features {
            font-size: 14px;
            text-align: center;
            opacity: 0.8;
        }
        
        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .download-btn:hover {
            background: #218838;
        }
        
        .instructions {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #0073aa;
        }
        
        .step {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 3px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Instant Image Generator for ThemeForest</h1>
        
        <div class="instructions">
            <h3>📋 Quick Instructions:</h3>
            <p><strong>Right-click on each image below → "Save image as..." → Upload to ThemeForest</strong></p>
        </div>
        
        <div class="step">
            <h3>1. Thumbnail (80x80px) - Required</h3>
            <div class="thumbnail" id="thumbnail">
                <div class="robot-icon">🤖</div>
                <div class="ai-text">AI</div>
            </div>
            <p><strong>Action:</strong> Right-click above → Save as "thumbnail.png"</p>
        </div>
        
        <div class="step">
            <h3>2. Inline Preview Image (590x300px) - Required</h3>
            <div class="preview" id="preview">
                <div class="preview-title">AI CopyToolkit</div>
                <div class="preview-subtitle">Smart Copywriting Assistant</div>
                <div class="preview-features">
                    60+ AI Models • 50+ Templates • Multi-language Support<br>
                    Generate Professional Copy in Seconds
                </div>
            </div>
            <p><strong>Action:</strong> Right-click above → Save as "preview.png"</p>
        </div>
        
        <div class="instructions">
            <h3>⚡ Alternative Quick Methods:</h3>
            
            <h4>Method 1: Use Online Tools</h4>
            <ul>
                <li><strong>Thumbnail:</strong> <a href="https://favicon.io/emoji-favicons/robot/" target="_blank">favicon.io robot icon</a></li>
                <li><strong>Preview:</strong> <a href="https://canva.com" target="_blank">Canva.com</a> → Custom size 590x300</li>
            </ul>
            
            <h4>Method 2: Simple Text Images</h4>
            <ul>
                <li><strong>Thumbnail:</strong> Blue square with "AI" text</li>
                <li><strong>Preview:</strong> Blue rectangle with "AI CopyToolkit" text</li>
            </ul>
        </div>
        
        <div class="step">
            <h3>3. Files Included - Select These:</h3>
            <ul>
                <li>✅ <strong>PHP Files</strong> (your plugin code)</li>
                <li>✅ <strong>CSS Files</strong> (your styling)</li>
                <li>✅ <strong>JS Files</strong> (your JavaScript)</li>
                <li>❌ Don't select PSD, HTML, etc.</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>🎯 Upload Process:</h3>
            <ol>
                <li>Save both images above (right-click → save)</li>
                <li>Go back to ThemeForest upload form</li>
                <li><strong>Thumbnail:</strong> Upload the 80x80 image</li>
                <li><strong>Inline Preview Image:</strong> Upload the 590x300 image</li>
                <li><strong>Files Included:</strong> Select PHP Files, CSS Files, JS Files</li>
                <li>Complete rest of form and submit</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #d4edda; border-radius: 8px;">
            <h3>✅ Ready to Upload!</h3>
            <p>Save both images above, then go back to ThemeForest and upload them as individual files (not ZIP).</p>
        </div>
    </div>
    
    <script>
        // Add some interactivity
        document.getElementById('thumbnail').addEventListener('click', function() {
            alert('Right-click on this thumbnail and select "Save image as..." to download it as thumbnail.png');
        });
        
        document.getElementById('preview').addEventListener('click', function() {
            alert('Right-click on this preview and select "Save image as..." to download it as preview.png');
        });
    </script>
</body>
</html>
