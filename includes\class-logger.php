<?php
/**
 * Logger class for error handling and debugging
 *
 * @package AI_CopyToolkit
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI_CopyToolkit_Logger class
 */
class AI_CopyToolkit_Logger {
    
    /**
     * Instance
     *
     * @var AI_CopyToolkit_Logger
     */
    private static $instance = null;
    
    /**
     * Log levels
     */
    const EMERGENCY = 'emergency';
    const ALERT = 'alert';
    const CRITICAL = 'critical';
    const ERROR = 'error';
    const WARNING = 'warning';
    const NOTICE = 'notice';
    const INFO = 'info';
    const DEBUG = 'debug';
    
    /**
     * Log file path
     *
     * @var string
     */
    private $log_file;
    
    /**
     * Get instance
     *
     * @return AI_CopyToolkit_Logger
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $upload_dir = wp_upload_dir();
        $log_dir = $upload_dir['basedir'] . '/ai-copytoolkit-logs';
        
        // Create log directory if it doesn't exist
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
            
            // Add .htaccess to protect log files
            $htaccess_content = "Order deny,allow\nDeny from all\n";
            file_put_contents($log_dir . '/.htaccess', $htaccess_content);
        }
        
        $this->log_file = $log_dir . '/ai-copytoolkit-' . date('Y-m-d') . '.log';
        
        // Hook into WordPress error handling
        add_action('wp_ajax_ai_copytoolkit_clear_logs', array($this, 'clear_logs'));
        add_action('wp_ajax_ai_copytoolkit_download_logs', array($this, 'download_logs'));
    }
    
    /**
     * Log a message
     *
     * @param string $level
     * @param string $message
     * @param array $context
     */
    public function log($level, $message, $context = array()) {
        $settings = AI_CopyToolkit::get_settings();
        
        // Check if logging is enabled
        if (!isset($settings['enable_logging']) || !$settings['enable_logging']) {
            return;
        }
        
        $timestamp = current_time('Y-m-d H:i:s');
        $user_id = get_current_user_id();
        $ip_address = $this->get_client_ip();
        
        $log_entry = array(
            'timestamp' => $timestamp,
            'level' => strtoupper($level),
            'message' => $message,
            'user_id' => $user_id,
            'ip_address' => $ip_address,
            'context' => $context
        );
        
        $formatted_entry = $this->format_log_entry($log_entry);
        
        // Write to file
        $this->write_to_file($formatted_entry);
        
        // Also log to WordPress error log for critical errors
        if (in_array($level, array(self::EMERGENCY, self::ALERT, self::CRITICAL, self::ERROR))) {
            error_log('AI CopyToolkit [' . strtoupper($level) . ']: ' . $message);
        }
        
        // Clean up old log files
        $this->cleanup_old_logs();
    }
    
    /**
     * Log emergency message
     *
     * @param string $message
     * @param array $context
     */
    public function emergency($message, $context = array()) {
        $this->log(self::EMERGENCY, $message, $context);
    }
    
    /**
     * Log alert message
     *
     * @param string $message
     * @param array $context
     */
    public function alert($message, $context = array()) {
        $this->log(self::ALERT, $message, $context);
    }
    
    /**
     * Log critical message
     *
     * @param string $message
     * @param array $context
     */
    public function critical($message, $context = array()) {
        $this->log(self::CRITICAL, $message, $context);
    }
    
    /**
     * Log error message
     *
     * @param string $message
     * @param array $context
     */
    public function error($message, $context = array()) {
        $this->log(self::ERROR, $message, $context);
    }
    
    /**
     * Log warning message
     *
     * @param string $message
     * @param array $context
     */
    public function warning($message, $context = array()) {
        $this->log(self::WARNING, $message, $context);
    }
    
    /**
     * Log notice message
     *
     * @param string $message
     * @param array $context
     */
    public function notice($message, $context = array()) {
        $this->log(self::NOTICE, $message, $context);
    }
    
    /**
     * Log info message
     *
     * @param string $message
     * @param array $context
     */
    public function info($message, $context = array()) {
        $this->log(self::INFO, $message, $context);
    }
    
    /**
     * Log debug message
     *
     * @param string $message
     * @param array $context
     */
    public function debug($message, $context = array()) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $this->log(self::DEBUG, $message, $context);
        }
    }
    
    /**
     * Log API request
     *
     * @param string $endpoint
     * @param array $request_data
     * @param array $response_data
     * @param float $duration
     */
    public function log_api_request($endpoint, $request_data, $response_data, $duration) {
        $context = array(
            'endpoint' => $endpoint,
            'request_size' => strlen(wp_json_encode($request_data)),
            'response_size' => strlen(wp_json_encode($response_data)),
            'duration' => $duration,
            'tokens_used' => isset($response_data['usage']['total_tokens']) ? $response_data['usage']['total_tokens'] : 0
        );
        
        $this->info('API Request completed', $context);
    }
    
    /**
     * Log content generation
     *
     * @param int $user_id
     * @param string $template_name
     * @param string $model
     * @param int $tokens_used
     * @param float $generation_time
     * @param string $status
     */
    public function log_content_generation($user_id, $template_name, $model, $tokens_used, $generation_time, $status) {
        $context = array(
            'user_id' => $user_id,
            'template' => $template_name,
            'model' => $model,
            'tokens_used' => $tokens_used,
            'generation_time' => $generation_time,
            'status' => $status
        );
        
        if ($status === 'completed') {
            $this->info('Content generation completed', $context);
        } else {
            $this->error('Content generation failed', $context);
        }
    }
    
    /**
     * Get recent log entries
     *
     * @param int $limit
     * @param string $level
     * @return array
     */
    public function get_recent_logs($limit = 100, $level = null) {
        if (!file_exists($this->log_file)) {
            return array();
        }
        
        $logs = array();
        $handle = fopen($this->log_file, 'r');
        
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                $entry = json_decode($line, true);
                
                if ($entry && (!$level || $entry['level'] === strtoupper($level))) {
                    $logs[] = $entry;
                }
                
                if (count($logs) >= $limit) {
                    break;
                }
            }
            
            fclose($handle);
        }
        
        return array_reverse($logs);
    }
    
    /**
     * Get log statistics
     *
     * @return array
     */
    public function get_log_stats() {
        if (!file_exists($this->log_file)) {
            return array(
                'total_entries' => 0,
                'error_count' => 0,
                'warning_count' => 0,
                'info_count' => 0,
                'file_size' => 0
            );
        }
        
        $stats = array(
            'total_entries' => 0,
            'error_count' => 0,
            'warning_count' => 0,
            'info_count' => 0,
            'file_size' => filesize($this->log_file)
        );
        
        $handle = fopen($this->log_file, 'r');
        
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                $entry = json_decode($line, true);
                
                if ($entry) {
                    $stats['total_entries']++;
                    
                    switch ($entry['level']) {
                        case 'ERROR':
                        case 'CRITICAL':
                        case 'EMERGENCY':
                        case 'ALERT':
                            $stats['error_count']++;
                            break;
                        case 'WARNING':
                            $stats['warning_count']++;
                            break;
                        case 'INFO':
                        case 'NOTICE':
                            $stats['info_count']++;
                            break;
                    }
                }
            }
            
            fclose($handle);
        }
        
        return $stats;
    }
    
    /**
     * Clear logs
     */
    public function clear_logs() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        $upload_dir = wp_upload_dir();
        $log_dir = $upload_dir['basedir'] . '/ai-copytoolkit-logs';
        
        $files = glob($log_dir . '/*.log');
        $deleted = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $deleted++;
            }
        }
        
        wp_send_json_success(sprintf(__('Deleted %d log files', 'ai-copytoolkit'), $deleted));
    }
    
    /**
     * Download logs
     */
    public function download_logs() {
        check_ajax_referer('ai_copytoolkit_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'ai-copytoolkit'));
        }
        
        if (!file_exists($this->log_file)) {
            wp_die(__('No log file found', 'ai-copytoolkit'));
        }
        
        $filename = 'ai-copytoolkit-logs-' . date('Y-m-d') . '.log';
        
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($this->log_file));
        
        readfile($this->log_file);
        exit;
    }
    
    /**
     * Format log entry
     *
     * @param array $entry
     * @return string
     */
    private function format_log_entry($entry) {
        return wp_json_encode($entry) . "\n";
    }
    
    /**
     * Write to log file
     *
     * @param string $content
     */
    private function write_to_file($content) {
        if (is_writable(dirname($this->log_file))) {
            file_put_contents($this->log_file, $content, FILE_APPEND | LOCK_EX);
        }
    }
    
    /**
     * Clean up old log files
     */
    private function cleanup_old_logs() {
        $upload_dir = wp_upload_dir();
        $log_dir = $upload_dir['basedir'] . '/ai-copytoolkit-logs';
        
        $files = glob($log_dir . '/*.log');
        $cutoff_time = time() - (30 * DAY_IN_SECONDS); // Keep logs for 30 days
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff_time) {
                unlink($file);
            }
        }
    }
    
    /**
     * Get client IP address
     *
     * @return string
     */
    private function get_client_ip() {
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                
                $ip = trim($ip);
                
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
}
